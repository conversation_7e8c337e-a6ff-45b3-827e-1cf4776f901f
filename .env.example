# Zerodha Kite API Configuration
KITE_API_KEY=your_api_key_here
KITE_API_SECRET=your_api_secret_here
KITE_ACCESS_TOKEN=your_access_token_here

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Application Configuration
APP_NAME=StockX
APP_VERSION=1.0.0
DEBUG=True
LOG_LEVEL=INFO

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Database Configuration
# For local development
DATABASE_URL=postgresql://stockx:stockx@localhost:5432/stockx
# For Docker (automatically set in docker-compose.yml)
# DATABASE_URL=****************************************/stockx

# Authentication Configuration
SECRET_KEY=your-super-secret-key-change-in-production-min-32-chars
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
PASSWORD_RESET_EXPIRE_MINUTES=30

# Email Configuration (for password reset)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=StockX

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3000
