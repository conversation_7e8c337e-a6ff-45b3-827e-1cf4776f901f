# 🔐 StockX Authentication System

A comprehensive, secure, and scalable user authentication system for the StockX platform.

## 🎯 Overview

The StockX authentication system provides:
- **User Registration & Login** with email verification
- **JWT Token Authentication** with refresh tokens
- **Password Reset** via email
- **User Profile Management** with preferences
- **Protected Routes** and middleware
- **Rate Limiting** and security features

## 🏗️ Architecture

```
React Frontend ←→ FastAPI Backend ←→ PostgreSQL Database
     ↓                ↓                    ↓
JWT Tokens      Auth Middleware      User/Profile Tables
     ↓                ↓                    ↓
Protected UI    Protected APIs       Password Hashing
```

### Components
- **Frontend**: React with authentication context and protected routes
- **Backend**: FastAPI with JWT authentication and email services
- **Database**: PostgreSQL with user, profile, and token tables
- **Email**: SMTP integration for password reset and notifications

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Profiles Table
```sql
CREATE TABLE profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    full_name VARCHAR(255),
    mobile_number VARCHAR(15),
    profile_picture TEXT,
    preferences JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Password Reset Tokens Table
```sql
CREATE TABLE password_reset_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔑 API Endpoints

### Authentication
| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/api/v1/auth/register` | POST | Register new user | No |
| `/api/v1/auth/login` | POST | Login user | No |
| `/api/v1/auth/refresh` | POST | Refresh access token | Refresh Token |
| `/api/v1/auth/me` | GET | Get current user info | Yes |
| `/api/v1/auth/forgot-password` | POST | Request password reset | No |
| `/api/v1/auth/reset-password` | POST | Reset password with token | No |
| `/api/v1/auth/change-password` | POST | Change password | Yes |
| `/api/v1/auth/profile` | PUT | Update user profile | Yes |

### Request/Response Examples

#### Register User
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "full_name": "John Doe",
  "mobile_number": "+1234567890"
}
```

#### Login User
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}

# Response
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

## 🔒 Security Features

### Password Security
- **Bcrypt Hashing**: Passwords hashed with bcrypt + salt
- **Strength Validation**: Minimum 8 chars, uppercase, lowercase, digit, special char
- **Password Reset**: Time-limited tokens (30 minutes)

### Token Security
- **JWT Tokens**: Signed with HS256 algorithm
- **Access Tokens**: Short-lived (30 minutes)
- **Refresh Tokens**: Longer-lived (7 days)
- **Token Rotation**: New tokens on refresh

### API Security
- **Rate Limiting**: Protect against brute force attacks
- **CORS**: Configured for frontend domain
- **HTTPS**: Enforced in production
- **Input Validation**: Pydantic models with validation

## 🎨 Frontend Integration

### Authentication Context
```javascript
import { useAuth } from './context/AuthContext';

function MyComponent() {
  const { user, login, logout, isAuthenticated } = useAuth();
  
  if (!isAuthenticated) {
    return <LoginForm />;
  }
  
  return <Dashboard user={user} />;
}
```

### Protected Routes
```javascript
import ProtectedRoute from './components/Auth/ProtectedRoute';

<Route path="/dashboard" element={
  <ProtectedRoute>
    <Dashboard />
  </ProtectedRoute>
} />
```

### API Integration
```javascript
import { authAPI } from './services/authAPI';

// Login
const response = await authAPI.login({
  email: '<EMAIL>',
  password: 'password'
});

// Auto token refresh
// Handled automatically by axios interceptors
```

## 🚀 Setup & Installation

### 1. Database Setup
```bash
# Run database setup script
./scripts/setup_database.sh

# Or manually create database
createdb stockx
psql stockx -c "CREATE USER stockx WITH PASSWORD 'stockx';"
psql stockx -c "GRANT ALL PRIVILEGES ON DATABASE stockx TO stockx;"
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

Required environment variables:
```env
# Database
DATABASE_URL=postgresql://stockx:stockx@localhost:5432/stockx

# Authentication
SECRET_KEY=your-super-secret-key-min-32-chars
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Email (for password reset)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
```

### 3. Run Migrations
```bash
# Install dependencies
pip install -r requirements.txt

# Create and run migrations
alembic revision --autogenerate -m "Initial auth tables"
alembic upgrade head
```

### 4. Start Application
```bash
# Full stack with authentication
./scripts/start_with_auth.sh

# Or manually
uvicorn app.main:app --reload
cd frontend && npm start
```

## 🧪 Testing

### Manual Testing
```bash
# Register user
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!",
    "full_name": "Test User"
  }'

# Login user
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!"
  }'

# Access protected endpoint
curl -X GET http://localhost:8000/api/v1/auth/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Frontend Testing
1. Visit `http://localhost:3000`
2. Click "Sign Up" to create account
3. Login with credentials
4. Access protected features
5. Test password reset flow

## 🔧 Configuration

### JWT Configuration
```python
# app/config.py
SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7
```

### Email Configuration
```python
# Email settings for password reset
SMTP_HOST = "smtp.gmail.com"
SMTP_PORT = 587
SMTP_USERNAME = "<EMAIL>"
SMTP_PASSWORD = "your-app-password"
```

### Database Configuration
```python
# Database connection
DATABASE_URL = "postgresql://user:pass@localhost:5432/stockx"
```

## 🛡️ Security Best Practices

### Production Deployment
1. **Use strong SECRET_KEY** (32+ random characters)
2. **Enable HTTPS** for all endpoints
3. **Configure CORS** for your domain only
4. **Set secure cookie flags** for sessions
5. **Use environment variables** for secrets
6. **Enable rate limiting** on auth endpoints
7. **Monitor failed login attempts**
8. **Regular security audits**

### Password Policy
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one digit
- At least one special character
- No common passwords

### Token Management
- Short-lived access tokens (30 minutes)
- Longer refresh tokens (7 days)
- Automatic token rotation
- Secure token storage (httpOnly cookies in production)

## 📈 Monitoring & Analytics

### Metrics to Track
- Registration rate
- Login success/failure rate
- Password reset requests
- Token refresh frequency
- API endpoint usage
- Failed authentication attempts

### Logging
- Authentication events
- Password reset requests
- Failed login attempts
- Token refresh events
- Profile updates

## 🔮 Future Enhancements

### Planned Features
- **Email Verification**: Verify email addresses on registration
- **Two-Factor Authentication**: SMS/TOTP 2FA support
- **Social Login**: Google, GitHub, LinkedIn integration
- **Session Management**: Active session tracking and revocation
- **Account Lockout**: Temporary lockout after failed attempts
- **Audit Logs**: Comprehensive user activity logging
- **Role-Based Access**: Admin, user, premium user roles
- **API Keys**: Generate API keys for programmatic access

### Security Enhancements
- **Device Tracking**: Track login devices and locations
- **Suspicious Activity Detection**: ML-based fraud detection
- **Password Breach Checking**: Check against known breaches
- **Advanced Rate Limiting**: Per-user and per-IP limits
- **Captcha Integration**: Prevent automated attacks

## 📚 Additional Resources

- [FastAPI Security Documentation](https://fastapi.tiangolo.com/tutorial/security/)
- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)
- [OWASP Authentication Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html)
- [React Authentication Patterns](https://kentcdodds.com/blog/authentication-in-react-applications)
