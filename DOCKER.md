# 🐳 StockX Docker Guide

Complete Docker setup and management guide for the StockX platform with authentication.

## 🎯 Overview

StockX uses Docker for consistent development and production environments. All services run in containers:

- **PostgreSQL**: User data and authentication
- **Redis**: Message broker and caching
- **FastAPI**: Backend API with authentication
- **Celery Worker**: Asynchronous task processing
- **React Frontend**: User interface
- **Flower**: Celery monitoring (optional)

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (PostgreSQL)  │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Monitoring    │    │   Worker        │    │   Cache/Broker  │
│   (Flower)      │    │   (Celery)      │    │   (Redis)       │
│   Port: 5555    │    │   Background    │    │   Port: 6379    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### 1. Prerequisites
```bash
# Install Docker and Docker Compose
# macOS
brew install docker docker-compose

# Ubuntu
sudo apt-get install docker.io docker-compose

# Windows
# Download Docker Desktop from docker.com
```

### 2. Setup Environment
```bash
# Clone repository
git clone <repository-url>
cd stockx

# Setup environment
cp .env.example .env
# Edit .env with your configuration
```

### 3. Start Services
```bash
# Option 1: Use startup script (recommended)
./scripts/start_with_auth.sh

# Option 2: Use Docker manager
./scripts/docker-manager.sh start

# Option 3: Manual docker-compose
docker-compose up -d
```

### 4. Access Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Authentication**: http://localhost:8000/api/v1/auth/

## 🔧 Docker Manager

The `docker-manager.sh` script provides easy management of Docker services:

```bash
# Service Management
./scripts/docker-manager.sh start      # Start all services
./scripts/docker-manager.sh stop       # Stop all services
./scripts/docker-manager.sh restart    # Restart all services
./scripts/docker-manager.sh status     # Show service status

# Monitoring
./scripts/docker-manager.sh logs       # Show all logs
./scripts/docker-manager.sh logs api   # Show specific service logs

# Development
./scripts/docker-manager.sh shell api  # Open shell in API container
./scripts/docker-manager.sh db-shell   # Open PostgreSQL shell
./scripts/docker-manager.sh redis-cli  # Open Redis CLI

# Maintenance
./scripts/docker-manager.sh build      # Build images
./scripts/docker-manager.sh clean      # Clean up resources
./scripts/docker-manager.sh reset      # Reset everything
./scripts/docker-manager.sh migrate    # Run database migrations

# Production
./scripts/docker-manager.sh prod       # Start in production mode
```

## 📋 Docker Compose Profiles

### Default Profile
```bash
docker-compose up -d
```
Starts: `postgres`, `redis`, `api`, `worker`

### Frontend Profile
```bash
docker-compose --profile frontend up -d
```
Starts: Default + `frontend`

### Monitoring Profile
```bash
docker-compose --profile monitoring up -d
```
Starts: Default + `flower`

### All Services
```bash
docker-compose --profile frontend --profile monitoring up -d
```
Starts: All services including frontend and monitoring

## 🗄️ Database Management

### Database Access
```bash
# PostgreSQL shell
./scripts/docker-manager.sh db-shell

# Or manually
docker-compose exec postgres psql -U stockx -d stockx
```

### Database Migrations
```bash
# Run migrations
./scripts/docker-manager.sh migrate

# Or manually
docker-compose exec api alembic upgrade head

# Create new migration
docker-compose exec api alembic revision --autogenerate -m "Description"
```

### Database Backup
```bash
# Backup database
docker-compose exec postgres pg_dump -U stockx stockx > backup.sql

# Restore database
docker-compose exec -T postgres psql -U stockx -d stockx < backup.sql
```

## 🔍 Monitoring & Debugging

### Service Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f api
docker-compose logs -f worker
docker-compose logs -f postgres

# Last N lines
docker-compose logs --tail=100 api
```

### Service Status
```bash
# Docker compose status
docker-compose ps

# Detailed status
./scripts/docker-manager.sh status

# Health checks
curl http://localhost:8000/health
```

### Container Shell Access
```bash
# API container
docker-compose exec api bash

# Database container
docker-compose exec postgres bash

# Worker container
docker-compose exec worker bash
```

### Flower Monitoring
```bash
# Start Flower
docker-compose --profile monitoring up -d flower

# Access dashboard
open http://localhost:5555
```

## 🔧 Development Workflow

### Hot Reload Development
The development setup includes hot reload for both frontend and backend:

```bash
# Start development environment
docker-compose up -d

# Code changes are automatically reflected:
# - Backend: FastAPI auto-reload
# - Frontend: React hot reload
# - Worker: Auto-restart on code changes
```

### Development Overrides
The `docker-compose.override.yml` file provides development-specific configurations:

- Source code mounting for hot reload
- Debug logging
- Development database settings
- Exposed ports for direct access

### Testing in Docker
```bash
# Run tests
./scripts/docker-manager.sh test

# Or manually
docker-compose exec api python -m pytest

# Run specific test
docker-compose exec api python -m pytest tests/test_auth.py
```

## 🚀 Production Deployment

### Production Configuration
```bash
# Create production environment file
cp .env.example .env.prod
# Edit with production settings

# Start production services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Production Features
- **Optimized images**: Multi-stage builds
- **Security**: No exposed ports, secure networks
- **Performance**: Gunicorn with multiple workers
- **Monitoring**: Health checks and logging
- **SSL/TLS**: Nginx reverse proxy ready

### Production Checklist
- [ ] Set strong passwords in `.env.prod`
- [ ] Configure SSL certificates
- [ ] Set up reverse proxy (Nginx)
- [ ] Configure monitoring and alerting
- [ ] Set up backup strategy
- [ ] Configure log rotation
- [ ] Set resource limits

## 🔒 Security Considerations

### Container Security
```bash
# Run containers as non-root user
# (Already configured in Dockerfiles)

# Use specific image versions
# (Specified in docker-compose.yml)

# Limit container resources
docker-compose up -d --memory=512m --cpus=1.0
```

### Network Security
```bash
# Production network isolation
# (Configured in docker-compose.prod.yml)

# No exposed ports in production
# (Access via reverse proxy only)
```

### Data Security
```bash
# Encrypted volumes for sensitive data
# Database password protection
# Redis authentication
# JWT secret key protection
```

## 🛠️ Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using the port
lsof -i :8000
lsof -i :3000
lsof -i :5432

# Stop conflicting services
sudo systemctl stop postgresql
sudo systemctl stop redis
```

#### Database Connection Issues
```bash
# Check database status
docker-compose logs postgres

# Restart database
docker-compose restart postgres

# Check database connectivity
docker-compose exec api pg_isready -h postgres -p 5432
```

#### Memory Issues
```bash
# Check container resource usage
docker stats

# Increase Docker memory limit
# (Docker Desktop > Settings > Resources)

# Clean up unused resources
docker system prune -f
```

#### Migration Issues
```bash
# Reset migrations
docker-compose exec api alembic downgrade base
docker-compose exec api alembic upgrade head

# Check migration status
docker-compose exec api alembic current
docker-compose exec api alembic history
```

### Debug Mode
```bash
# Enable debug logging
export DEBUG=True
export LOG_LEVEL=DEBUG

# Restart with debug
docker-compose restart api worker
```

### Clean Reset
```bash
# Complete reset (removes all data)
./scripts/docker-manager.sh reset

# Or manually
docker-compose down -v
docker system prune -f
docker-compose build
docker-compose up -d
```

## 📊 Performance Optimization

### Resource Allocation
```yaml
# In docker-compose.yml
services:
  api:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
```

### Database Optimization
```bash
# PostgreSQL configuration
# Edit config/postgresql.conf for production

# Connection pooling
# Configure in application settings
```

### Caching Strategy
```bash
# Redis optimization
# Configure memory limits and persistence

# Application caching
# Use Redis for session and data caching
```

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [PostgreSQL Docker Image](https://hub.docker.com/_/postgres)
- [Redis Docker Image](https://hub.docker.com/_/redis)
- [FastAPI Docker Guide](https://fastapi.tiangolo.com/deployment/docker/)

## 🆘 Support

For Docker-related issues:
1. Check the troubleshooting section above
2. Review Docker logs: `docker-compose logs`
3. Check system resources: `docker stats`
4. Create an issue in the repository with logs and system info
