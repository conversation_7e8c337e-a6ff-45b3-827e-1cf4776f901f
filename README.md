# StockX - Advanced Stock Analysis Platform

A full-stack application for real-time stock data analysis featuring a FastAPI backend with Celery for asynchronous processing and a modern React frontend for interactive stock analysis.

## 🚀 Features

### 🔧 Backend (FastAPI + Celery)
- **Asynchronous Stock Data Fetching**: Uses Celery for non-blocking API calls
- **Real-time Market Data**: Integrates with Zerodha Kite API
- **Task Status Tracking**: Monitor progress of data fetch requests
- **Multiple Exchanges**: Support for NSE, BSE, NFO, BFO, CDS, MCX
- **API Documentation**: Auto-generated docs with FastAPI
- **Health Monitoring**: Built-in health checks and monitoring

### 🎨 Frontend (React)
- **Modern UI**: Dark theme with glass morphism effects
- **Stock Search**: Autocomplete search with popular Indian stocks
- **Interactive Charts**: Real-time price charts with multiple view types
- **Watchlist Management**: Add/remove stocks with live updates
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Real-time Updates**: Auto-refresh stock data every 30 seconds

### 🐳 Infrastructure
- **Docker Support**: Easy deployment with Docker Compose
- **Redis Integration**: Message broker and result backend
- **Monitoring**: Flower dashboard for Celery task monitoring

## 🏗️ Architecture

```
React Frontend → FastAPI Backend → Celery Task Queue → Zerodha Kite API
     ↓               ↓                    ↓
WebSocket/HTTP   Task Management    Redis (Broker/Backend)
     ↓               ↓                    ↓
Real-time UI ← Status Polling ← Task Results & Stock Data
```

### Component Overview
- **Frontend**: React app with Material-UI components
- **Backend**: FastAPI with async endpoints
- **Worker**: Celery workers for background tasks
- **Broker**: Redis for message queuing and caching
- **API**: Zerodha Kite for live market data

## 📋 Prerequisites

### Backend
- Python 3.11+
- Redis server
- Zerodha Kite API credentials

### Frontend
- Node.js 16+
- npm or yarn

### Optional
- Docker & Docker Compose (recommended for easy setup)

## 🛠️ Installation

### Quick Start (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd StockX
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your Kite API credentials
   ```

3. **Start full-stack application**
   ```bash
   # With Docker (recommended)
   docker-compose --profile frontend up -d

   # Or manually
   ./scripts/start_full_stack.sh
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Docs: http://localhost:8000/docs

### Manual Setup

#### Backend Setup
1. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start Redis server**
   ```bash
   redis-server
   ```

3. **Start Celery worker**
   ```bash
   celery -A celery_app worker --loglevel=info
   ```

4. **Start FastAPI application**
   ```bash
   uvicorn app.main:app --reload
   ```

#### Frontend Setup
1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install Node.js dependencies**
   ```bash
   npm install
   ```

3. **Start React development server**
   ```bash
   npm start
   ```

### Docker Deployment

1. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your credentials
   ```

2. **Start backend only**
   ```bash
   docker-compose up -d
   ```

3. **Start full-stack (backend + frontend)**
   ```bash
   docker-compose --profile frontend up -d
   ```

4. **Start with monitoring (includes Flower)**
   ```bash
   docker-compose --profile frontend --profile monitoring up -d
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `KITE_API_KEY` | Zerodha Kite API Key | Required |
| `KITE_API_SECRET` | Zerodha Kite API Secret | Required |
| `KITE_ACCESS_TOKEN` | Kite Access Token | Required |
| `REDIS_URL` | Redis connection URL | `redis://localhost:6379/0` |
| `API_HOST` | FastAPI host | `0.0.0.0` |
| `API_PORT` | FastAPI port | `8000` |
| `DEBUG` | Debug mode | `True` |
| `LOG_LEVEL` | Logging level | `INFO` |

### Zerodha Kite API Setup

1. Create a Kite Connect app at [developers.kite.trade](https://developers.kite.trade)
2. Get your `api_key` and `api_secret`
3. Generate an access token using the login flow
4. Add credentials to your `.env` file

## 📚 API Endpoints

### Stock Data Endpoints

- **GET** `/api/v1/get_stock_data` - Request detailed stock quote
- **GET** `/api/v1/get_stock_ltp` - Request Last Traded Price
- **GET** `/api/v1/task_status/{task_id}` - Check task status
- **GET** `/api/v1/health` - Health check
- **GET** `/api/v1/exchanges` - List supported exchanges

### Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🔄 Usage Examples

### Request Stock Data

```bash
curl "http://localhost:8000/api/v1/get_stock_data?symbol=RELIANCE&exchange=NSE"
```

Response:
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "PENDING",
  "message": "Stock data fetch task submitted for RELIANCE"
}
```

### Check Task Status

```bash
curl "http://localhost:8000/api/v1/task_status/abc123-def456-ghi789"
```

Response:
```json
{
  "task_id": "abc123-def456-ghi789",
  "status": "SUCCESS",
  "result": {
    "last_price": 2450.50,
    "volume": 1234567,
    "ohlc": {
      "open": 2440.00,
      "high": 2460.00,
      "low": 2435.00,
      "close": 2450.50
    },
    "net_change": 10.50
  }
}
```

## 🐳 Docker Services

| Service | Port | Description | Profile |
|---------|------|-------------|---------|
| `api` | 8000 | FastAPI application | default |
| `worker` | - | Celery worker | default |
| `redis` | 6379 | Redis broker/backend | default |
| `frontend` | 3000 | React application | frontend |
| `flower` | 5555 | Celery monitoring | monitoring |

## 📊 Monitoring

### Flower (Celery Monitoring)

Access Flower dashboard at http://localhost:5555 when running with monitoring profile.

### Health Checks

- Application: http://localhost:8000/health
- Celery: http://localhost:8000/api/v1/health

## 🧪 Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest tests/

# Run with coverage
pytest --cov=app tests/
```

## 🔒 Security Considerations

- Store API credentials securely using environment variables
- Use HTTPS in production
- Implement rate limiting
- Add authentication for API endpoints
- Validate and sanitize input parameters

## 🚀 Production Deployment

1. **Use production-grade Redis**
2. **Set up proper logging and monitoring**
3. **Configure reverse proxy (nginx)**
4. **Use secrets management**
5. **Set up SSL/TLS certificates**
6. **Configure auto-scaling for workers**

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For issues and questions, please create an issue in the repository.
