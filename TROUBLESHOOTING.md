# 🔧 StockX Troubleshooting Guide

Common issues and solutions for the StockX platform.

## 🚨 Common Issues

### 1. Backend API Not Accessible (http://localhost:8000/)

#### Symptoms
- `curl http://localhost:8000/` returns connection refused
- Frontend can't connect to backend
- API documentation not accessible

#### Solutions

**Check if services are running:**
```bash
docker-compose ps
```

**Check API logs:**
```bash
docker-compose logs api
```

**Restart API service:**
```bash
docker-compose restart api
```

**Check if port is available:**
```bash
lsof -i :8000
# Kill any conflicting process
sudo kill -9 <PID>
```

**Test API manually:**
```bash
# Test health endpoint
curl http://localhost:8000/health

# Test root endpoint
curl http://localhost:8000/

# Test with verbose output
curl -v http://localhost:8000/health
```

### 2. Frontend Build Errors

#### Symptoms
- `Identifier 'authAPI' has already been declared`
- Module build failed errors
- Frontend not starting

#### Solutions

**Clear node modules and reinstall:**
```bash
cd frontend
rm -rf node_modules package-lock.json
npm install
```

**Check for duplicate declarations:**
```bash
# Search for duplicate exports
grep -n "export.*authAPI" frontend/src/services/authAPI.js
```

**Restart frontend service:**
```bash
docker-compose restart frontend
```

### 3. Database Connection Issues

#### Symptoms
- Database connection failed
- Migration errors
- `pg_isready` fails

#### Solutions

**Check database status:**
```bash
docker-compose logs postgres
docker-compose exec postgres pg_isready -U stockx -d stockx
```

**Reset database:**
```bash
docker-compose down -v
docker-compose up -d postgres
```

**Run migrations manually:**
```bash
docker-compose exec api alembic upgrade head
```

**Check database connectivity:**
```bash
docker-compose exec postgres psql -U stockx -d stockx -c "SELECT 1;"
```

### 4. Dependency Conflicts

#### Symptoms
- `ResolutionImpossible: The conflict is caused by: sqlalchemy==2.0.23 databases 0.8.0 depends on sqlalchemy<1.5`
- Docker build fails with dependency errors
- Import errors in backend

#### Solutions

**Check dependencies:**
```bash
python scripts/check_dependencies.py
```

**Use compatible SQLAlchemy version:**
```bash
# Edit requirements.txt to use:
sqlalchemy==1.4.53
databases[postgresql]==0.8.0
alembic==1.12.1
```

**Rebuild containers:**
```bash
docker-compose build --no-cache
```

**Alternative: Use SQLAlchemy 2.0 without databases:**
```bash
# Use requirements-alt.txt
cp requirements-alt.txt requirements.txt
# Then rebuild
```

**Check Python path:**
```bash
docker-compose exec api python -c "import sys; print(sys.path)"
```

### 5. Authentication Issues

#### Symptoms
- Login not working
- Token errors
- Protected routes not accessible

#### Solutions

**Check auth endpoints:**
```bash
# Test registration
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!","full_name":"Test User"}'

# Test login
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'
```

**Check database tables:**
```bash
docker-compose exec postgres psql -U stockx -d stockx -c "\dt"
```

**Reset auth data:**
```bash
docker-compose exec postgres psql -U stockx -d stockx -c "TRUNCATE users, profiles, password_reset_tokens CASCADE;"
```

## 🛠️ Diagnostic Commands

### Service Health Check
```bash
# Run comprehensive health check
./scripts/test_docker_services.sh

# Quick service test
./scripts/quick_start.sh
```

### Container Inspection
```bash
# Check container status
docker-compose ps

# Check container logs
docker-compose logs -f [service]

# Get container shell
docker-compose exec [service] bash

# Check container resources
docker stats
```

### Network Debugging
```bash
# Check port bindings
docker-compose port api 8000
docker-compose port frontend 3000

# Test internal connectivity
docker-compose exec api curl http://postgres:5432
docker-compose exec api curl http://redis:6379
```

### Database Debugging
```bash
# Database shell
docker-compose exec postgres psql -U stockx -d stockx

# Check tables
docker-compose exec postgres psql -U stockx -d stockx -c "\dt"

# Check users
docker-compose exec postgres psql -U stockx -d stockx -c "SELECT * FROM users;"

# Check migrations
docker-compose exec api alembic current
docker-compose exec api alembic history
```

## 🔄 Reset Procedures

### Soft Reset (Keep Data)
```bash
docker-compose restart
```

### Medium Reset (Rebuild Containers)
```bash
docker-compose down
docker-compose build
docker-compose up -d
```

### Hard Reset (Remove Everything)
```bash
docker-compose down -v
docker system prune -f
docker-compose build --no-cache
docker-compose up -d
```

### Database Reset
```bash
# Remove only database volume
docker-compose down
docker volume rm stockx_postgres_data
docker-compose up -d
```

## 📊 Performance Issues

### High Memory Usage
```bash
# Check container memory usage
docker stats

# Limit container memory
docker-compose up -d --memory=512m
```

### Slow Startup
```bash
# Check startup logs
docker-compose logs -f api

# Increase timeout in health checks
# Edit docker-compose.yml healthcheck intervals
```

### Database Performance
```bash
# Check database connections
docker-compose exec postgres psql -U stockx -d stockx -c "SELECT * FROM pg_stat_activity;"

# Optimize database
docker-compose exec postgres psql -U stockx -d stockx -c "VACUUM ANALYZE;"
```

## 🔍 Debugging Steps

### 1. Check Environment
```bash
# Verify .env file exists
ls -la .env

# Check environment variables
docker-compose config
```

### 2. Check Dependencies
```bash
# Backend dependencies
docker-compose exec api pip list

# Frontend dependencies
docker-compose exec frontend npm list
```

### 3. Check Logs
```bash
# All services
docker-compose logs

# Specific service with timestamps
docker-compose logs -t -f api

# Last N lines
docker-compose logs --tail=50 api
```

### 4. Test Endpoints
```bash
# Health check
curl -f http://localhost:8000/health

# API documentation
curl -f http://localhost:8000/docs

# Frontend
curl -f http://localhost:3000
```

## 🆘 Emergency Procedures

### Complete System Reset
```bash
#!/bin/bash
echo "🚨 Emergency Reset - This will remove ALL data!"
read -p "Are you sure? (yes/no): " confirm
if [ "$confirm" = "yes" ]; then
    docker-compose down -v
    docker system prune -af
    docker volume prune -f
    rm -rf node_modules
    cp .env.example .env
    echo "✅ System reset complete. Please reconfigure .env"
fi
```

### Backup Before Reset
```bash
# Backup database
docker-compose exec postgres pg_dump -U stockx stockx > backup_$(date +%Y%m%d_%H%M%S).sql

# Backup environment
cp .env .env.backup
```

### Recovery
```bash
# Restore database
docker-compose exec -T postgres psql -U stockx -d stockx < backup_YYYYMMDD_HHMMSS.sql

# Restore environment
cp .env.backup .env
```

## 📞 Getting Help

### Log Collection
```bash
# Collect all logs
mkdir -p logs
docker-compose logs > logs/all_services.log
docker-compose logs api > logs/api.log
docker-compose logs frontend > logs/frontend.log
docker-compose logs postgres > logs/postgres.log
docker-compose logs redis > logs/redis.log
```

### System Information
```bash
# Docker version
docker --version
docker-compose --version

# System resources
docker system df
docker system info

# Container information
docker-compose ps
docker-compose config
```

### Create Issue Report
When reporting issues, include:
1. Error message and logs
2. Steps to reproduce
3. System information
4. Docker and docker-compose versions
5. Environment configuration (without secrets)

## 🔧 Maintenance

### Regular Maintenance
```bash
# Clean up unused resources
docker system prune -f

# Update images
docker-compose pull
docker-compose build

# Check for updates
docker-compose config --services | xargs -I {} docker-compose pull {}
```

### Monitoring
```bash
# Resource usage
docker stats

# Service health
./scripts/test_docker_services.sh

# Log monitoring
docker-compose logs -f --tail=100
```
