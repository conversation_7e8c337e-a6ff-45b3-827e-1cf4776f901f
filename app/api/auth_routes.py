"""Authentication API routes."""

from datetime import <PERSON><PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from loguru import logger

from app.database import get_db
from app.middleware.auth import (
    get_current_active_user, 
    get_current_verified_user,
    verify_refresh_token,
    security
)
from app.services.auth_service import AuthService
from app.services.email_service import email_service
from app.models.auth import (
    UserCreate, UserLogin, UserResponse, UserWithProfile,
    UserProfileUpdate, UserProfileResponse,
    Token, TokenData, PasswordResetRequest, PasswordReset,
    ChangePassword, User
)
from app.config import settings


router = APIRouter(prefix="/api/v1/auth", tags=["authentication"])


@router.post("/register", response_model=UserWithProfile, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Register a new user."""
    try:
        # Create user
        user = AuthService.create_user(
            db=db,
            email=user_data.email,
            password=user_data.password,
            full_name=user_data.full_name,
            mobile_number=user_data.mobile_number
        )
        
        # Send welcome email in background
        background_tasks.add_task(
            email_service.send_welcome_email,
            user.email,
            user_data.full_name
        )
        
        # Return user with profile
        return UserWithProfile(
            id=str(user.id),
            email=user.email,
            is_verified=user.is_verified,
            is_active=user.is_active,
            created_at=user.created_at,
            profile=UserProfileResponse(
                id=str(user.profile.id),
                user_id=str(user.profile.user_id),
                full_name=user.profile.full_name,
                mobile_number=user.profile.mobile_number,
                profile_picture=user.profile.profile_picture,
                preferences=user.profile.preferences,
                created_at=user.profile.created_at,
                updated_at=user.profile.updated_at
            ) if user.profile else None
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=Token)
async def login(
    user_credentials: UserLogin,
    db: Session = Depends(get_db)
):
    """Authenticate user and return tokens."""
    # Authenticate user
    user = AuthService.authenticate_user(
        db, user_credentials.email, user_credentials.password
    )
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create tokens
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    refresh_token_expires = timedelta(days=settings.refresh_token_expire_days)
    
    access_token = AuthService.create_access_token(
        data={"sub": str(user.id), "email": user.email},
        expires_delta=access_token_expires
    )
    
    refresh_token = AuthService.create_refresh_token(
        data={"sub": str(user.id), "email": user.email},
        expires_delta=refresh_token_expires
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=settings.access_token_expire_minutes * 60
    )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token_data: TokenData = Depends(verify_refresh_token),
    db: Session = Depends(get_db)
):
    """Refresh access token using refresh token."""
    # Get user
    user = AuthService.get_user_by_id(db, token_data.user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    # Create new tokens
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    refresh_token_expires = timedelta(days=settings.refresh_token_expire_days)
    
    access_token = AuthService.create_access_token(
        data={"sub": str(user.id), "email": user.email},
        expires_delta=access_token_expires
    )
    
    refresh_token = AuthService.create_refresh_token(
        data={"sub": str(user.id), "email": user.email},
        expires_delta=refresh_token_expires
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=settings.access_token_expire_minutes * 60
    )


@router.get("/me", response_model=UserWithProfile)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information."""
    return UserWithProfile(
        id=str(current_user.id),
        email=current_user.email,
        is_verified=current_user.is_verified,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        profile=UserProfileResponse(
            id=str(current_user.profile.id),
            user_id=str(current_user.profile.user_id),
            full_name=current_user.profile.full_name,
            mobile_number=current_user.profile.mobile_number,
            profile_picture=current_user.profile.profile_picture,
            preferences=current_user.profile.preferences,
            created_at=current_user.profile.created_at,
            updated_at=current_user.profile.updated_at
        ) if current_user.profile else None
    )


@router.put("/profile", response_model=UserProfileResponse)
async def update_profile(
    profile_data: UserProfileUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update user profile."""
    try:
        # Update profile
        updated_profile = AuthService.update_user_profile(
            db=db,
            user_id=current_user.id,
            profile_data=profile_data.dict(exclude_unset=True)
        )
        
        if not updated_profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        
        return UserProfileResponse(
            id=str(updated_profile.id),
            user_id=str(updated_profile.user_id),
            full_name=updated_profile.full_name,
            mobile_number=updated_profile.mobile_number,
            profile_picture=updated_profile.profile_picture,
            preferences=updated_profile.preferences,
            created_at=updated_profile.created_at,
            updated_at=updated_profile.updated_at
        )
        
    except Exception as e:
        logger.error(f"Profile update error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )


@router.post("/forgot-password")
async def forgot_password(
    request: PasswordResetRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Request password reset."""
    # Check if user exists
    user = AuthService.get_user_by_email(db, request.email)
    
    # Always return success to prevent email enumeration
    message = "If the email exists, a password reset link has been sent"
    
    if user:
        try:
            # Create reset token
            reset_token = AuthService.create_reset_token(db, user.id)
            
            # Send reset email in background
            background_tasks.add_task(
                email_service.send_password_reset_email,
                user.email,
                reset_token,
                user.profile.full_name if user.profile else None
            )
            
        except Exception as e:
            logger.error(f"Password reset error: {str(e)}")
    
    return {"message": message}


@router.post("/reset-password")
async def reset_password(
    reset_data: PasswordReset,
    db: Session = Depends(get_db)
):
    """Reset password using token."""
    try:
        success = AuthService.reset_password(
            db=db,
            token=reset_data.token,
            new_password=reset_data.new_password
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        return {"message": "Password reset successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password reset error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )


@router.post("/change-password")
async def change_password(
    password_data: ChangePassword,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Change user password."""
    try:
        success = AuthService.change_password(
            db=db,
            user_id=current_user.id,
            current_password=password_data.current_password,
            new_password=password_data.new_password
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        return {"message": "Password changed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password change error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )
