"""Stock data API routes."""

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from typing import Optional
from loguru import logger

from app.models.stock import (
    StockDataRequest, 
    TaskResponse, 
    TaskResult, 
    TaskStatus,
    Exchange
)
from app.tasks.stock_tasks import fetch_stock_data, fetch_stock_ltp, health_check
from celery_app import celery_app

router = APIRouter(prefix="/api/v1", tags=["stock"])


@router.get("/get_stock_data", response_model=TaskResponse)
async def request_stock_data(
    symbol: str = Query(..., description="Stock symbol (e.g., RELIANCE, INFY)"),
    exchange: Exchange = Query(default=Exchange.NSE, description="Exchange")
) -> TaskResponse:
    """
    Request stock data fetch. Returns a task ID for tracking progress.
    
    This endpoint triggers an asynchronous Celery task to fetch detailed
    stock quote data from the Zerodha Kite API.
    """
    try:
        # Validate symbol
        if not symbol or len(symbol.strip()) == 0:
            raise HTTPException(status_code=400, detail="Symbol cannot be empty")
        
        symbol = symbol.strip().upper()
        
        logger.info(f"Received stock data request for {symbol} on {exchange.value}")
        
        # Submit Celery task
        task = fetch_stock_data.delay(symbol, exchange.value)
        
        logger.info(f"Submitted task {task.id} for {symbol}")
        
        return TaskResponse(
            task_id=task.id,
            status=TaskStatus.PENDING,
            message=f"Stock data fetch task submitted for {symbol}"
        )
        
    except Exception as e:
        logger.error(f"Error submitting stock data task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to submit task: {str(e)}")


@router.get("/get_stock_ltp", response_model=TaskResponse)
async def request_stock_ltp(
    symbol: str = Query(..., description="Stock symbol (e.g., RELIANCE, INFY)"),
    exchange: Exchange = Query(default=Exchange.NSE, description="Exchange")
) -> TaskResponse:
    """
    Request Last Traded Price (LTP) fetch. Returns a task ID for tracking progress.
    
    This endpoint triggers an asynchronous Celery task to fetch the current
    Last Traded Price from the Zerodha Kite API.
    """
    try:
        # Validate symbol
        if not symbol or len(symbol.strip()) == 0:
            raise HTTPException(status_code=400, detail="Symbol cannot be empty")
        
        symbol = symbol.strip().upper()
        
        logger.info(f"Received LTP request for {symbol} on {exchange.value}")
        
        # Submit Celery task
        task = fetch_stock_ltp.delay(symbol, exchange.value)
        
        logger.info(f"Submitted LTP task {task.id} for {symbol}")
        
        return TaskResponse(
            task_id=task.id,
            status=TaskStatus.PENDING,
            message=f"LTP fetch task submitted for {symbol}"
        )
        
    except Exception as e:
        logger.error(f"Error submitting LTP task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to submit task: {str(e)}")


@router.get("/task_status/{task_id}", response_model=TaskResult)
async def get_task_status(task_id: str) -> TaskResult:
    """
    Get the status and result of a Celery task.
    
    Returns the current status of the task and the result if completed.
    """
    try:
        # Get task result from Celery
        task_result = celery_app.AsyncResult(task_id)
        
        if task_result.state == 'PENDING':
            return TaskResult(
                task_id=task_id,
                status=TaskStatus.PENDING,
                result=None,
                error=None,
                progress=None
            )
        
        elif task_result.state == 'STARTED':
            # Get progress information if available
            progress = task_result.info if isinstance(task_result.info, dict) else None
            return TaskResult(
                task_id=task_id,
                status=TaskStatus.STARTED,
                result=None,
                error=None,
                progress=progress
            )
        
        elif task_result.state == 'SUCCESS':
            # Task completed successfully
            result_data = task_result.result
            
            # Extract stock data from result
            stock_data = None
            if isinstance(result_data, dict) and 'data' in result_data:
                stock_data = result_data['data']
            
            return TaskResult(
                task_id=task_id,
                status=TaskStatus.SUCCESS,
                result=stock_data,
                error=None,
                progress=None
            )
        
        elif task_result.state == 'FAILURE':
            # Task failed
            error_info = task_result.info if isinstance(task_result.info, dict) else {'error': str(task_result.info)}
            
            return TaskResult(
                task_id=task_id,
                status=TaskStatus.FAILURE,
                result=None,
                error=error_info.get('error', 'Unknown error'),
                progress=error_info
            )
        
        else:
            # Other states (RETRY, REVOKED, etc.)
            return TaskResult(
                task_id=task_id,
                status=TaskStatus(task_result.state),
                result=None,
                error=None,
                progress=task_result.info if isinstance(task_result.info, dict) else None
            )
            
    except Exception as e:
        logger.error(f"Error getting task status for {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get task status: {str(e)}")


@router.get("/health", response_model=TaskResponse)
async def health_check_endpoint() -> TaskResponse:
    """
    Health check endpoint that verifies Celery and Kite API connectivity.
    """
    try:
        # Submit health check task
        task = health_check.delay()
        
        return TaskResponse(
            task_id=task.id,
            status=TaskStatus.PENDING,
            message="Health check task submitted"
        )
        
    except Exception as e:
        logger.error(f"Error submitting health check task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to submit health check: {str(e)}")


@router.get("/exchanges")
async def get_supported_exchanges():
    """Get list of supported exchanges."""
    return {
        "exchanges": [exchange.value for exchange in Exchange],
        "default": Exchange.NSE.value
    }
