"""Database configuration and connection management."""

import databases
import sqlalchemy
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings

# Database URL
DATABASE_URL = settings.database_url

# Create database instance
database = databases.Database(DATABASE_URL)

# SQLAlchemy engine
engine = sqlalchemy.create_engine(DATABASE_URL)

# Session maker
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Database dependency
async def get_database():
    """Get database connection."""
    return database

def get_db():
    """Get database session for sync operations."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Database connection events
async def connect_to_db():
    """Connect to database."""
    await database.connect()

async def close_db_connection():
    """Close database connection."""
    await database.disconnect()
