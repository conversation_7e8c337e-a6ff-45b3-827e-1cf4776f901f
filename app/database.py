"""Database configuration and connection management."""

import databases
import sqlalchemy
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings

# Database URL
DATABASE_URL = settings.database_url

# Create database instance for async operations
database = databases.Database(DATABASE_URL)

# SQLAlchemy engine for sync operations
engine = sqlalchemy.create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=settings.debug
)

# Session maker for sync operations
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# Database dependency for async operations
async def get_database():
    """Get database connection for async operations."""
    return database

# Database dependency for sync operations
def get_db():
    """Get database session for sync operations."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Database connection events
async def connect_to_db():
    """Connect to database."""
    try:
        await database.connect()
        print("✅ Database connected successfully")
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        raise

async def close_db_connection():
    """Close database connection."""
    try:
        await database.disconnect()
        print("✅ Database disconnected successfully")
    except Exception as e:
        print(f"❌ Database disconnection failed: {str(e)}")

# Create tables
def create_tables():
    """Create database tables."""
    Base.metadata.create_all(bind=engine)
