"""Stock data models."""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum


class Exchange(str, Enum):
    """Supported exchanges."""
    NSE = "NSE"
    BSE = "BSE"
    NFO = "NFO"
    BFO = "BFO"
    CDS = "CDS"
    MCX = "MCX"


class TaskStatus(str, Enum):
    """Celery task status."""
    PENDING = "PENDING"
    STARTED = "STARTED"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    RETRY = "RETRY"
    REVOKED = "REVOKED"


class StockDataRequest(BaseModel):
    """Request model for stock data."""
    symbol: str = Field(..., description="Stock symbol (e.g., RELIANCE, INFY)")
    exchange: Exchange = Field(default=Exchange.NSE, description="Exchange")
    
    class Config:
        json_schema_extra = {
            "example": {
                "symbol": "RELIANCE",
                "exchange": "NSE"
            }
        }


class StockQuote(BaseModel):
    """Stock quote data model."""
    instrument_token: Optional[int] = None
    timestamp: Optional[datetime] = None
    last_price: Optional[float] = Field(None, description="Last traded price")
    last_quantity: Optional[int] = None
    last_trade_time: Optional[datetime] = None
    average_price: Optional[float] = None
    volume: Optional[int] = None
    buy_quantity: Optional[int] = None
    sell_quantity: Optional[int] = None
    ohlc: Optional[Dict[str, float]] = Field(None, description="Open, High, Low, Close")
    net_change: Optional[float] = None
    oi: Optional[int] = Field(None, description="Open Interest")
    oi_day_high: Optional[int] = None
    oi_day_low: Optional[int] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "last_price": 2450.50,
                "volume": 1234567,
                "ohlc": {
                    "open": 2440.00,
                    "high": 2460.00,
                    "low": 2435.00,
                    "close": 2450.50
                },
                "net_change": 10.50,
                "timestamp": "2024-01-15T15:30:00"
            }
        }


class TaskResponse(BaseModel):
    """Task response model."""
    task_id: str = Field(..., description="Celery task ID")
    status: TaskStatus = Field(..., description="Task status")
    message: str = Field(..., description="Status message")
    
    class Config:
        json_schema_extra = {
            "example": {
                "task_id": "abc123-def456-ghi789",
                "status": "PENDING",
                "message": "Task submitted successfully"
            }
        }


class TaskResult(BaseModel):
    """Task result model."""
    task_id: str = Field(..., description="Celery task ID")
    status: TaskStatus = Field(..., description="Task status")
    result: Optional[StockQuote] = Field(None, description="Stock data result")
    error: Optional[str] = Field(None, description="Error message if failed")
    progress: Optional[Dict[str, Any]] = Field(None, description="Task progress info")
    
    class Config:
        json_schema_extra = {
            "example": {
                "task_id": "abc123-def456-ghi789",
                "status": "SUCCESS",
                "result": {
                    "last_price": 2450.50,
                    "volume": 1234567,
                    "ohlc": {
                        "open": 2440.00,
                        "high": 2460.00,
                        "low": 2435.00,
                        "close": 2450.50
                    }
                }
            }
        }
