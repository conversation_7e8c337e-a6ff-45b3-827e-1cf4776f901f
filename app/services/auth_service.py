"""Authentication service utilities."""

import secrets
import uuid
from datetime import datetime, timedelta
from typing import Optional, Union
from passlib.context import CryptContext
from jose import JW<PERSON>rror, jwt
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.config import settings
from app.models.auth import User, UserProfile, PasswordResetToken
from app.models.auth import TokenData


# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """Authentication service class."""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """Hash a password."""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create access token."""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create refresh token."""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(days=settings.refresh_token_expire_days)
        
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> Optional[TokenData]:
        """Verify and decode token."""
        try:
            payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
            
            # Check token type
            if payload.get("type") != token_type:
                return None
            
            user_id: str = payload.get("sub")
            email: str = payload.get("email")
            
            if user_id is None:
                return None
            
            return TokenData(user_id=user_id, email=email)
        except JWTError:
            return None
    
    @staticmethod
    def generate_reset_token() -> str:
        """Generate a secure reset token."""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def create_user(db: Session, email: str, password: str, full_name: Optional[str] = None, 
                   mobile_number: Optional[str] = None) -> User:
        """Create a new user."""
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == email.lower()).first()
        if existing_user:
            raise ValueError("User with this email already exists")
        
        # Create user
        hashed_password = AuthService.get_password_hash(password)
        user = User(
            email=email.lower(),
            password_hash=hashed_password
        )
        db.add(user)
        db.flush()  # Flush to get the user ID
        
        # Create user profile
        profile = UserProfile(
            user_id=user.id,
            full_name=full_name,
            mobile_number=mobile_number,
            preferences={}
        )
        db.add(profile)
        db.commit()
        db.refresh(user)
        
        return user
    
    @staticmethod
    def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password."""
        user = db.query(User).filter(
            and_(User.email == email.lower(), User.is_active == True)
        ).first()
        
        if not user or not AuthService.verify_password(password, user.password_hash):
            return None
        
        return user
    
    @staticmethod
    def get_user_by_id(db: Session, user_id: Union[str, uuid.UUID]) -> Optional[User]:
        """Get user by ID."""
        if isinstance(user_id, str):
            user_id = uuid.UUID(user_id)
        
        return db.query(User).filter(
            and_(User.id == user_id, User.is_active == True)
        ).first()
    
    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[User]:
        """Get user by email."""
        return db.query(User).filter(
            and_(User.email == email.lower(), User.is_active == True)
        ).first()
    
    @staticmethod
    def create_reset_token(db: Session, user_id: Union[str, uuid.UUID]) -> str:
        """Create password reset token."""
        if isinstance(user_id, str):
            user_id = uuid.UUID(user_id)
        
        # Invalidate existing tokens
        db.query(PasswordResetToken).filter(
            and_(
                PasswordResetToken.user_id == user_id,
                PasswordResetToken.is_used == False,
                PasswordResetToken.expires_at > datetime.utcnow()
            )
        ).update({"is_used": True})
        
        # Create new token
        token = AuthService.generate_reset_token()
        expires_at = datetime.utcnow() + timedelta(minutes=settings.password_reset_expire_minutes)
        
        reset_token = PasswordResetToken(
            user_id=user_id,
            token=token,
            expires_at=expires_at
        )
        db.add(reset_token)
        db.commit()
        
        return token
    
    @staticmethod
    def verify_reset_token(db: Session, token: str) -> Optional[User]:
        """Verify password reset token and return user."""
        reset_token = db.query(PasswordResetToken).filter(
            and_(
                PasswordResetToken.token == token,
                PasswordResetToken.is_used == False,
                PasswordResetToken.expires_at > datetime.utcnow()
            )
        ).first()
        
        if not reset_token:
            return None
        
        user = db.query(User).filter(User.id == reset_token.user_id).first()
        return user
    
    @staticmethod
    def reset_password(db: Session, token: str, new_password: str) -> bool:
        """Reset user password using token."""
        reset_token = db.query(PasswordResetToken).filter(
            and_(
                PasswordResetToken.token == token,
                PasswordResetToken.is_used == False,
                PasswordResetToken.expires_at > datetime.utcnow()
            )
        ).first()
        
        if not reset_token:
            return False
        
        # Update password
        user = db.query(User).filter(User.id == reset_token.user_id).first()
        if not user:
            return False
        
        user.password_hash = AuthService.get_password_hash(new_password)
        reset_token.is_used = True
        
        db.commit()
        return True
    
    @staticmethod
    def update_user_profile(db: Session, user_id: Union[str, uuid.UUID], 
                          profile_data: dict) -> Optional[UserProfile]:
        """Update user profile."""
        if isinstance(user_id, str):
            user_id = uuid.UUID(user_id)
        
        profile = db.query(UserProfile).filter(UserProfile.user_id == user_id).first()
        if not profile:
            return None
        
        # Update fields
        for field, value in profile_data.items():
            if hasattr(profile, field) and value is not None:
                setattr(profile, field, value)
        
        profile.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(profile)
        
        return profile
    
    @staticmethod
    def change_password(db: Session, user_id: Union[str, uuid.UUID], 
                       current_password: str, new_password: str) -> bool:
        """Change user password."""
        if isinstance(user_id, str):
            user_id = uuid.UUID(user_id)
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return False
        
        # Verify current password
        if not AuthService.verify_password(current_password, user.password_hash):
            return False
        
        # Update password
        user.password_hash = AuthService.get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        db.commit()
        
        return True
