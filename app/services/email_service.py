"""Email service for authentication."""

import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from jinja2 import Template
from typing import List, Optional
from loguru import logger

from app.config import settings


class EmailService:
    """Email service for sending authentication emails."""
    
    def __init__(self):
        self.smtp_host = settings.smtp_host
        self.smtp_port = settings.smtp_port
        self.smtp_username = settings.smtp_username
        self.smtp_password = settings.smtp_password
        self.from_email = settings.smtp_from_email or settings.smtp_username
        self.from_name = settings.smtp_from_name
    
    async def send_email(self, to_emails: List[str], subject: str, 
                        html_content: str, text_content: Optional[str] = None) -> bool:
        """Send email using SMTP."""
        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = ", ".join(to_emails)
            
            # Add text content
            if text_content:
                text_part = MIMEText(text_content, "plain")
                message.attach(text_part)
            
            # Add HTML content
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)
            
            # Send email
            await aiosmtplib.send(
                message,
                hostname=self.smtp_host,
                port=self.smtp_port,
                start_tls=True,
                username=self.smtp_username,
                password=self.smtp_password,
            )
            
            logger.info(f"Email sent successfully to {to_emails}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_emails}: {str(e)}")
            return False
    
    async def send_password_reset_email(self, email: str, reset_token: str, 
                                      user_name: Optional[str] = None) -> bool:
        """Send password reset email."""
        reset_url = f"{settings.frontend_url}/reset-password?token={reset_token}"
        
        # HTML template
        html_template = Template("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Password Reset - StockX</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
                .button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 Password Reset Request</h1>
                </div>
                <div class="content">
                    <h2>Hello{% if user_name %} {{ user_name }}{% endif %}!</h2>
                    <p>We received a request to reset your password for your StockX account.</p>
                    <p>Click the button below to reset your password:</p>
                    <p style="text-align: center;">
                        <a href="{{ reset_url }}" class="button">Reset Password</a>
                    </p>
                    <p>Or copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; background: #f0f0f0; padding: 10px; border-radius: 4px;">
                        {{ reset_url }}
                    </p>
                    <div class="warning">
                        <strong>⚠️ Important:</strong>
                        <ul>
                            <li>This link will expire in 30 minutes</li>
                            <li>If you didn't request this reset, please ignore this email</li>
                            <li>For security, never share this link with anyone</li>
                        </ul>
                    </div>
                </div>
                <div class="footer">
                    <p>This email was sent by StockX - Advanced Stock Analysis Platform</p>
                    <p>If you have any questions, please contact our support team.</p>
                </div>
            </div>
        </body>
        </html>
        """)
        
        # Text template
        text_template = Template("""
        Password Reset Request - StockX
        
        Hello{% if user_name %} {{ user_name }}{% endif %}!
        
        We received a request to reset your password for your StockX account.
        
        Please visit the following link to reset your password:
        {{ reset_url }}
        
        Important:
        - This link will expire in 30 minutes
        - If you didn't request this reset, please ignore this email
        - For security, never share this link with anyone
        
        Best regards,
        StockX Team
        """)
        
        html_content = html_template.render(
            reset_url=reset_url,
            user_name=user_name
        )
        
        text_content = text_template.render(
            reset_url=reset_url,
            user_name=user_name
        )
        
        return await self.send_email(
            to_emails=[email],
            subject="Reset Your StockX Password",
            html_content=html_content,
            text_content=text_content
        )
    
    async def send_welcome_email(self, email: str, user_name: Optional[str] = None) -> bool:
        """Send welcome email to new users."""
        # HTML template
        html_template = Template("""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to StockX</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
                .button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                .features { background: white; padding: 20px; border-radius: 6px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 Welcome to StockX!</h1>
                </div>
                <div class="content">
                    <h2>Hello{% if user_name %} {{ user_name }}{% endif %}!</h2>
                    <p>Welcome to StockX - your advanced stock analysis platform! We're excited to have you on board.</p>
                    
                    <div class="features">
                        <h3>🚀 What you can do with StockX:</h3>
                        <ul>
                            <li>📊 Real-time stock data and analysis</li>
                            <li>📈 Interactive charts and technical indicators</li>
                            <li>💼 Portfolio tracking and management</li>
                            <li>🔔 Price alerts and notifications</li>
                            <li>📱 Mobile-responsive design</li>
                        </ul>
                    </div>
                    
                    <p style="text-align: center;">
                        <a href="{{ frontend_url }}" class="button">Start Analyzing Stocks</a>
                    </p>
                    
                    <p>If you have any questions or need help getting started, don't hesitate to reach out to our support team.</p>
                </div>
                <div class="footer">
                    <p>Happy trading!</p>
                    <p>The StockX Team</p>
                </div>
            </div>
        </body>
        </html>
        """)
        
        html_content = html_template.render(
            user_name=user_name,
            frontend_url=settings.frontend_url
        )
        
        return await self.send_email(
            to_emails=[email],
            subject="Welcome to StockX - Let's Start Analyzing!",
            html_content=html_content
        )


# Global email service instance
email_service = EmailService()
