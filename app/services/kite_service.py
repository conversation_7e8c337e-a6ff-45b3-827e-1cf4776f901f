"""Zerodha Kite API service."""

from kiteconnect import KiteConnect
from typing import Dict, Any, Optional
from loguru import logger
from app.config import settings
from app.models.stock import StockQuote, Exchange
from datetime import datetime


class KiteService:
    """Service for interacting with Zerodha Kite API."""
    
    def __init__(self):
        """Initialize Kite service."""
        self.kite = KiteConnect(api_key=settings.kite_api_key)
        if settings.kite_access_token:
            self.kite.set_access_token(settings.kite_access_token)
    
    def set_access_token(self, access_token: str) -> None:
        """Set access token for API calls."""
        self.kite.set_access_token(access_token)
        logger.info("Access token set for Kite API")
    
    def get_instrument_token(self, symbol: str, exchange: Exchange) -> Optional[int]:
        """Get instrument token for a symbol."""
        try:
            # Format the tradingsymbol for Kite API
            trading_symbol = f"{exchange.value}:{symbol}"
            
            # Get instruments list (this might be cached in production)
            instruments = self.kite.instruments(exchange.value)
            
            for instrument in instruments:
                if instrument['tradingsymbol'] == symbol:
                    return instrument['instrument_token']
            
            logger.warning(f"Instrument token not found for {trading_symbol}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting instrument token for {symbol}: {str(e)}")
            return None
    
    def get_ltp(self, symbol: str, exchange: Exchange) -> Optional[float]:
        """Get Last Traded Price for a symbol."""
        try:
            trading_symbol = f"{exchange.value}:{symbol}"
            ltp_data = self.kite.ltp([trading_symbol])
            
            if trading_symbol in ltp_data:
                return ltp_data[trading_symbol]['last_price']
            
            logger.warning(f"LTP not found for {trading_symbol}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting LTP for {symbol}: {str(e)}")
            raise
    
    def get_quote(self, symbol: str, exchange: Exchange) -> Optional[StockQuote]:
        """Get detailed quote for a symbol."""
        try:
            trading_symbol = f"{exchange.value}:{symbol}"
            quote_data = self.kite.quote([trading_symbol])
            
            if trading_symbol not in quote_data:
                logger.warning(f"Quote not found for {trading_symbol}")
                return None
            
            data = quote_data[trading_symbol]
            
            # Convert Kite API response to our StockQuote model
            stock_quote = StockQuote(
                instrument_token=data.get('instrument_token'),
                timestamp=datetime.now(),
                last_price=data.get('last_price'),
                last_quantity=data.get('last_quantity'),
                last_trade_time=self._parse_datetime(data.get('last_trade_time')),
                average_price=data.get('average_price'),
                volume=data.get('volume'),
                buy_quantity=data.get('buy_quantity'),
                sell_quantity=data.get('sell_quantity'),
                ohlc=data.get('ohlc'),
                net_change=data.get('net_change'),
                oi=data.get('oi'),
                oi_day_high=data.get('oi_day_high'),
                oi_day_low=data.get('oi_day_low')
            )
            
            logger.info(f"Successfully fetched quote for {trading_symbol}")
            return stock_quote
            
        except Exception as e:
            logger.error(f"Error getting quote for {symbol}: {str(e)}")
            raise
    
    def _parse_datetime(self, dt_str: Optional[str]) -> Optional[datetime]:
        """Parse datetime string from Kite API."""
        if not dt_str:
            return None
        
        try:
            # Kite API typically returns datetime in ISO format
            return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
        except (ValueError, AttributeError):
            logger.warning(f"Could not parse datetime: {dt_str}")
            return None
    
    def validate_connection(self) -> bool:
        """Validate API connection and token."""
        try:
            profile = self.kite.profile()
            logger.info(f"Kite API connection validated for user: {profile.get('user_name', 'Unknown')}")
            return True
        except Exception as e:
            logger.error(f"Kite API connection validation failed: {str(e)}")
            return False


# Global service instance
kite_service = KiteService()
