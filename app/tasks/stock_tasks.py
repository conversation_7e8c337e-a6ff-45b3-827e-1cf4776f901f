"""Celery tasks for stock data fetching."""

from celery import current_task
from celery.exceptions import Retry
from loguru import logger
from typing import Dict, Any
import time

from celery_app import celery_app
from app.services.kite_service import kite_service
from app.models.stock import Exchange, StockQuote


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 60})
def fetch_stock_data(self, symbol: str, exchange: str = "NSE") -> Dict[str, Any]:
    """
    Fetch detailed stock quote data from Kite API.
    
    Args:
        symbol: Stock symbol (e.g., 'RELIANCE')
        exchange: Exchange name (default: 'NSE')
    
    Returns:
        Dict containing stock quote data or error information
    """
    try:
        # Update task state to STARTED
        current_task.update_state(
            state='STARTED',
            meta={'message': f'Fetching data for {symbol} from {exchange}'}
        )
        
        logger.info(f"Starting stock data fetch for {symbol} on {exchange}")
        
        # Validate exchange
        try:
            exchange_enum = Exchange(exchange.upper())
        except ValueError:
            raise ValueError(f"Invalid exchange: {exchange}")
        
        # Validate Kite API connection
        if not kite_service.validate_connection():
            raise Exception("Kite API connection validation failed")
        
        # Update progress
        current_task.update_state(
            state='STARTED',
            meta={'message': f'Fetching quote for {symbol}...', 'progress': 50}
        )
        
        # Fetch stock quote
        quote = kite_service.get_quote(symbol, exchange_enum)
        
        if not quote:
            raise Exception(f"No data found for symbol {symbol} on {exchange}")
        
        # Convert to dict for JSON serialization
        result = quote.model_dump()
        
        logger.info(f"Successfully fetched stock data for {symbol}")
        
        return {
            'status': 'SUCCESS',
            'data': result,
            'symbol': symbol,
            'exchange': exchange,
            'message': f'Successfully fetched data for {symbol}'
        }
        
    except Exception as exc:
        logger.error(f"Error fetching stock data for {symbol}: {str(exc)}")
        
        # Update task state to FAILURE
        current_task.update_state(
            state='FAILURE',
            meta={
                'error': str(exc),
                'symbol': symbol,
                'exchange': exchange,
                'message': f'Failed to fetch data for {symbol}'
            }
        )
        
        # Re-raise the exception to mark task as failed
        raise exc


@celery_app.task(bind=True, autoretry_for=(Exception,), retry_kwargs={'max_retries': 3, 'countdown': 30})
def fetch_stock_ltp(self, symbol: str, exchange: str = "NSE") -> Dict[str, Any]:
    """
    Fetch Last Traded Price for a stock symbol.
    
    Args:
        symbol: Stock symbol (e.g., 'RELIANCE')
        exchange: Exchange name (default: 'NSE')
    
    Returns:
        Dict containing LTP data or error information
    """
    try:
        # Update task state to STARTED
        current_task.update_state(
            state='STARTED',
            meta={'message': f'Fetching LTP for {symbol} from {exchange}'}
        )
        
        logger.info(f"Starting LTP fetch for {symbol} on {exchange}")
        
        # Validate exchange
        try:
            exchange_enum = Exchange(exchange.upper())
        except ValueError:
            raise ValueError(f"Invalid exchange: {exchange}")
        
        # Validate Kite API connection
        if not kite_service.validate_connection():
            raise Exception("Kite API connection validation failed")
        
        # Fetch LTP
        ltp = kite_service.get_ltp(symbol, exchange_enum)
        
        if ltp is None:
            raise Exception(f"No LTP data found for symbol {symbol} on {exchange}")
        
        logger.info(f"Successfully fetched LTP for {symbol}: {ltp}")
        
        return {
            'status': 'SUCCESS',
            'data': {
                'symbol': symbol,
                'exchange': exchange,
                'last_price': ltp,
                'timestamp': time.time()
            },
            'message': f'Successfully fetched LTP for {symbol}'
        }
        
    except Exception as exc:
        logger.error(f"Error fetching LTP for {symbol}: {str(exc)}")
        
        # Update task state to FAILURE
        current_task.update_state(
            state='FAILURE',
            meta={
                'error': str(exc),
                'symbol': symbol,
                'exchange': exchange,
                'message': f'Failed to fetch LTP for {symbol}'
            }
        )
        
        # Re-raise the exception to mark task as failed
        raise exc


@celery_app.task(bind=True)
def health_check(self) -> Dict[str, Any]:
    """
    Health check task to verify Celery and Kite API connectivity.
    
    Returns:
        Dict containing health status
    """
    try:
        logger.info("Running health check task")
        
        # Check Kite API connection
        kite_healthy = kite_service.validate_connection()
        
        return {
            'status': 'SUCCESS',
            'celery_healthy': True,
            'kite_api_healthy': kite_healthy,
            'timestamp': time.time(),
            'message': 'Health check completed'
        }
        
    except Exception as exc:
        logger.error(f"Health check failed: {str(exc)}")
        return {
            'status': 'FAILURE',
            'celery_healthy': True,  # If we reach here, Celery is working
            'kite_api_healthy': False,
            'error': str(exc),
            'timestamp': time.time(),
            'message': 'Health check failed'
        }
