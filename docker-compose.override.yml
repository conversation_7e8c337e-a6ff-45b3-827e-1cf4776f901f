# Docker Compose override for development
# This file is automatically loaded by docker-compose
# and provides development-specific configurations

version: '3.8'

services:
  # Development overrides for API service
  api:
    volumes:
      # Mount source code for hot reload
      - .:/app
      - /app/__pycache__
    environment:
      # Development environment variables
      - DEBUG=True
      - LOG_LEVEL=DEBUG
      - PYTHONPATH=/app
    # Enable hot reload
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug

  # Development overrides for worker service
  worker:
    volumes:
      - .:/app
      - /app/__pycache__
    environment:
      - DEBUG=True
      - LOG_LEVEL=DEBUG
      - PYTHONPATH=/app
    # Enable auto-reload for worker
    command: celery -A celery_app worker --loglevel=debug --concurrency=2 --pool=solo

  # Development overrides for frontend service
  frontend:
    volumes:
      # Mount source code for hot reload
      - ./frontend:/app
      - /app/node_modules
    environment:
      # Development environment variables
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
    # Enable hot reload
    command: npm start

  # Development database with exposed port
  postgres:
    ports:
      - "5432:5432"
    environment:
      # Additional development settings
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      # Add development SQL scripts
      - ./scripts/dev-data.sql:/docker-entrypoint-initdb.d/99-dev-data.sql

  # Development Redis with exposed port
  redis:
    ports:
      - "6379:6379"
    # Enable Redis persistence in development
    command: redis-server --appendonly yes --appendfsync everysec
