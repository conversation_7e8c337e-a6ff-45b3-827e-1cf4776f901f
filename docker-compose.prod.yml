# Production Docker Compose configuration
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  # Production PostgreSQL with optimizations
  postgres:
    restart: always
    environment:
      # Production database settings
      POSTGRES_DB: stockx
      POSTGRES_USER: stockx
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      # Production data persistence
      - postgres_prod_data:/var/lib/postgresql/data
      # Production configuration
      - ./config/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    # Remove port exposure for security
    ports: []
    networks:
      - stockx-network

  # Production Redis with persistence
  redis:
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    # Remove port exposure for security
    ports: []
    networks:
      - stockx-network

  # Production API service
  api:
    restart: always
    environment:
      # Production environment variables
      - DEBUG=False
      - LOG_LEVEL=INFO
      - DATABASE_URL=postgresql://stockx:${POSTGRES_PASSWORD}@postgres:5432/stockx
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    # Remove volume mounts for security
    volumes: []
    # Production command
    command: gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
    networks:
      - stockx-network
    depends_on:
      - postgres
      - redis

  # Production worker service
  worker:
    restart: always
    environment:
      - DEBUG=False
      - LOG_LEVEL=INFO
      - DATABASE_URL=postgresql://stockx:${POSTGRES_PASSWORD}@postgres:5432/stockx
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    volumes: []
    command: celery -A celery_app worker --loglevel=info --concurrency=4
    networks:
      - stockx-network
    depends_on:
      - postgres
      - redis

  # Production frontend with Nginx
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    restart: always
    # Remove port exposure (will be handled by reverse proxy)
    ports: []
    networks:
      - stockx-network

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/ssl:/etc/nginx/ssl
    depends_on:
      - api
      - frontend
    networks:
      - stockx-network

  # Monitoring with Flower (optional)
  flower:
    restart: always
    environment:
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    volumes: []
    # Remove port exposure (access via nginx)
    ports: []
    networks:
      - stockx-network
    profiles:
      - monitoring

# Production volumes
volumes:
  postgres_prod_data:
    driver: local

# Production network
networks:
  stockx-network:
    driver: bridge
