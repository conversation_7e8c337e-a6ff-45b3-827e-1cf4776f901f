version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: stockx-postgres
    environment:
      POSTGRES_DB: stockx
      POSTGRES_USER: stockx
      POSTGRES_PASSWORD: stockx
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U stockx -d stockx"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis Cache & Message Broker
  redis:
    image: redis:7-alpine
    container_name: stockx-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  api:
    build: .
    container_name: stockx-api
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=****************************************/stockx
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  worker:
    build: .
    container_name: stockx-worker
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=****************************************/stockx
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: celery -A celery_app worker --loglevel=info --concurrency=4
    healthcheck:
      test: ["CMD", "celery", "-A", "celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  flower:
    build: .
    container_name: stockx-flower
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=****************************************/stockx
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    command: celery -A celery_app flower --port=5555
    profiles:
      - monitoring

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: stockx-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      api:
        condition: service_started
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm start
    profiles:
      - frontend

volumes:
  postgres_data:
  redis_data:
