# StockX Frontend

A modern React-based web application for stock analysis and portfolio management.

## 🎨 Features

### 🔍 Stock Search & Analysis
- **Smart Search**: Autocomplete search with popular Indian stocks
- **Real-time Data**: Live stock prices and market data
- **Interactive Charts**: Multiple chart types (Line, Area, Volume)
- **Technical Indicators**: Coming soon - RSI, MACD, Moving Averages

### 📊 Dashboard
- **Market Overview**: Quick stats and market summary
- **Watchlist Management**: Add/remove stocks from watchlist
- **Live Updates**: Auto-refresh stock data every 30 seconds
- **Responsive Design**: Works on desktop, tablet, and mobile

### 💼 Portfolio (Coming Soon)
- **Holdings Tracking**: Track your stock investments
- **P&L Analysis**: Real-time profit/loss calculations
- **Performance Charts**: Portfolio performance over time
- **Asset Allocation**: Visual breakdown of your investments

### ⚙️ Settings
- **Customization**: Theme, currency, refresh intervals
- **Notifications**: Price alerts and market updates
- **Trading Preferences**: Default exchange and order confirmations

## 🛠️ Technology Stack

- **React 18** - Modern React with hooks and context
- **Material-UI v5** - Beautiful, accessible components
- **React Query** - Data fetching and caching
- **Recharts** - Interactive charts and visualizations
- **Framer Motion** - Smooth animations and transitions
- **React Router v6** - Client-side routing
- **Axios** - HTTP client for API calls

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ and npm
- Backend API running on port 8000

### Installation

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env if needed
   ```

4. **Start development server**
   ```bash
   npm start
   ```

5. **Open in browser**
   ```
   http://localhost:3000
   ```

### Docker Development

```bash
# Start with frontend profile
docker-compose --profile frontend up -d

# Or start everything including frontend
docker-compose --profile frontend --profile monitoring up -d
```

## 📱 UI/UX Design

### Design System
- **Dark Theme**: Modern dark UI with glass morphism effects
- **Color Palette**: Purple gradient primary colors (#667eea, #764ba2)
- **Typography**: Inter font family for clean, readable text
- **Spacing**: Consistent 8px grid system
- **Animations**: Smooth transitions and micro-interactions

### Component Architecture
```
src/
├── components/
│   ├── Layout/          # Navigation and layout components
│   ├── Stock/           # Stock-related components
│   └── Charts/          # Chart and visualization components
├── pages/               # Main page components
├── context/             # React context for state management
├── services/            # API services and utilities
└── styles/              # Global styles and themes
```

### Key Components

#### StockSearch
- Autocomplete search with popular stocks
- Real-time filtering and suggestions
- Keyboard navigation support

#### StockCard
- Displays stock price and change
- Watchlist toggle functionality
- Hover animations and live indicators

#### StockChart
- Multiple chart types (Line, Area, Volume)
- Interactive tooltips and controls
- Responsive design for all screen sizes

#### Dashboard
- Market statistics overview
- Watchlist grid with real-time updates
- Quick action buttons

## 🎯 User Experience

### Navigation Flow
1. **Dashboard** - Overview of watchlist and market stats
2. **Stock Analysis** - Search and analyze individual stocks
3. **Portfolio** - Track investments (coming soon)
4. **Settings** - Customize app preferences

### Key User Interactions
- **Search**: Type to find stocks with autocomplete
- **Add to Watchlist**: Click bookmark icon on any stock
- **View Details**: Click stock card to see detailed analysis
- **Refresh Data**: Manual refresh or auto-refresh every 30s

### Responsive Design
- **Desktop**: Full sidebar navigation with expanded content
- **Tablet**: Collapsible sidebar with optimized layouts
- **Mobile**: Bottom navigation with touch-friendly interactions

## 🔧 Configuration

### Environment Variables
```env
REACT_APP_API_URL=http://localhost:8000  # Backend API URL
GENERATE_SOURCEMAP=false                 # Disable sourcemaps in production
```

### Build Configuration
- **Development**: Hot reload with source maps
- **Production**: Optimized bundle with code splitting
- **PWA Ready**: Service worker and manifest for offline support

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch
```

## 📦 Build & Deployment

### Development Build
```bash
npm start
```

### Production Build
```bash
npm run build
```

### Docker Build
```bash
docker build -t stockx-frontend .
docker run -p 3000:3000 stockx-frontend
```

## 🎨 Customization

### Theme Customization
Edit `src/index.js` to modify the Material-UI theme:
```javascript
const theme = createTheme({
  palette: {
    primary: { main: '#667eea' },
    secondary: { main: '#764ba2' },
    // ... other theme options
  },
});
```

### Adding New Components
1. Create component in appropriate directory
2. Export from index file
3. Add to routing if it's a page component
4. Update navigation if needed

## 🚀 Performance

### Optimization Features
- **Code Splitting**: Automatic route-based splitting
- **Lazy Loading**: Components loaded on demand
- **Memoization**: React.memo for expensive components
- **Caching**: React Query for API response caching
- **Bundle Analysis**: Webpack bundle analyzer included

### Performance Metrics
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Time to Interactive**: < 3.5s
- **Cumulative Layout Shift**: < 0.1

## 🔮 Future Enhancements

### Planned Features
- **Real-time WebSocket**: Live price updates
- **Advanced Charts**: Candlestick charts with indicators
- **Portfolio Management**: Complete investment tracking
- **News Integration**: Market news and analysis
- **Mobile App**: React Native version
- **PWA Features**: Offline support and push notifications

### Technical Improvements
- **TypeScript**: Gradual migration to TypeScript
- **Testing**: Increase test coverage to 90%+
- **Accessibility**: WCAG 2.1 AA compliance
- **Internationalization**: Multi-language support

## 📄 License

This project is licensed under the MIT License.
