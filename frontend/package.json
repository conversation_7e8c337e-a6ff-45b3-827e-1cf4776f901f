{"name": "stockx-frontend", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "axios": "^1.6.2", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "@mui/material": "^5.14.20", "@mui/icons-material": "^5.14.19", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "react-query": "^3.39.3", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "react-use-websocket": "^4.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}