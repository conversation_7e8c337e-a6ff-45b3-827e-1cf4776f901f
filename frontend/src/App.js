import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Container, Box } from '@mui/material';
import { motion } from 'framer-motion';

// Components
import Navbar from './components/Layout/Navbar';
import Sidebar from './components/Layout/Sidebar';
import Dashboard from './pages/Dashboard';
import StockAnalysis from './pages/StockAnalysis';
import Portfolio from './pages/Portfolio';
import Settings from './pages/Settings';

// Context
import { StockProvider } from './context/StockContext';
import { AuthProvider } from './context/AuthContext';

// Auth Components
import ProtectedRoute from './components/Auth/ProtectedRoute';
import Login from './pages/Login';
import Register from './pages/Register';

function App() {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <AuthProvider>
      <StockProvider>
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* Protected Routes */}
          <Route path="/*" element={
            <ProtectedRoute>
              <Box sx={{ display: 'flex', minHeight: '100vh' }}>
                {/* Sidebar */}
                <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />

                {/* Main Content */}
                <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                  {/* Navbar */}
                  <Navbar onMenuClick={toggleSidebar} />

                  {/* Page Content */}
                  <Box
                    component="main"
                    sx={{
                      flexGrow: 1,
                      p: 3,
                      mt: 8, // Account for navbar height
                      background: 'transparent',
                    }}
                  >
                    <Container maxWidth="xl">
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                      >
                        <Routes>
                          <Route path="/" element={<Dashboard />} />
                          <Route path="/dashboard" element={<Dashboard />} />
                          <Route path="/analysis" element={<StockAnalysis />} />
                          <Route path="/analysis/:symbol" element={<StockAnalysis />} />
                          <Route path="/portfolio" element={<Portfolio />} />
                          <Route path="/settings" element={<Settings />} />
                        </Routes>
                      </motion.div>
                    </Container>
                  </Box>
                </Box>
              </Box>
            </ProtectedRoute>
          } />
        </Routes>
      </StockProvider>
    </AuthProvider>
  );
}

export default App;
