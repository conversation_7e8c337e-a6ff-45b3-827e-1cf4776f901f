import React, { useState, useMemo } from 'react';
import {
  Box,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  Paper,
  Alert,
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar,
} from 'recharts';
import { motion } from 'framer-motion';

const StockChart = ({ stockData, symbol }) => {
  const [chartType, setChartType] = useState('line');
  const [timeframe, setTimeframe] = useState('1D');

  // Generate sample historical data (in a real app, this would come from API)
  const generateHistoricalData = (currentData) => {
    if (!currentData || !currentData.last_price) return [];

    const data = [];
    const currentPrice = currentData.last_price;
    const baseDate = new Date();
    
    // Generate 30 days of sample data
    for (let i = 29; i >= 0; i--) {
      const date = new Date(baseDate);
      date.setDate(date.getDate() - i);
      
      // Generate realistic price movements
      const randomChange = (Math.random() - 0.5) * 0.1; // ±5% random change
      const price = currentPrice * (1 + randomChange * (i / 30));
      const volume = Math.floor(Math.random() * 1000000) + 100000;
      
      data.push({
        date: date.toISOString().split('T')[0],
        time: date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        price: Math.max(price, currentPrice * 0.8), // Ensure price doesn't go too low
        volume: volume,
        high: price * 1.02,
        low: price * 0.98,
        open: price * 0.99,
        close: price,
      });
    }
    
    // Ensure the last data point matches current data
    if (data.length > 0) {
      data[data.length - 1] = {
        ...data[data.length - 1],
        price: currentData.last_price,
        close: currentData.last_price,
        high: currentData.ohlc?.high || currentData.last_price,
        low: currentData.ohlc?.low || currentData.last_price,
        open: currentData.ohlc?.open || currentData.last_price,
        volume: currentData.volume || data[data.length - 1].volume,
      };
    }
    
    return data;
  };

  const chartData = useMemo(() => {
    return generateHistoricalData(stockData);
  }, [stockData]);

  const handleChartTypeChange = (event, newType) => {
    if (newType !== null) {
      setChartType(newType);
    }
  };

  const handleTimeframeChange = (event, newTimeframe) => {
    if (newTimeframe !== null) {
      setTimeframe(newTimeframe);
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Paper
          sx={{
            p: 2,
            background: 'rgba(26, 29, 58, 0.95)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: 1,
          }}
        >
          <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1 }}>
            {data.date} {data.time}
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.primary', fontWeight: 600 }}>
            Price: ₹{data.price?.toFixed(2)}
          </Typography>
          {data.volume && (
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Volume: {data.volume.toLocaleString()}
            </Typography>
          )}
          {chartType === 'candlestick' && (
            <>
              <Typography variant="body2" sx={{ color: 'success.main' }}>
                High: ₹{data.high?.toFixed(2)}
              </Typography>
              <Typography variant="body2" sx={{ color: 'error.main' }}>
                Low: ₹{data.low?.toFixed(2)}
              </Typography>
              <Typography variant="body2" sx={{ color: 'info.main' }}>
                Open: ₹{data.open?.toFixed(2)}
              </Typography>
            </>
          )}
        </Paper>
      );
    }
    return null;
  };

  const renderChart = () => {
    if (!chartData || chartData.length === 0) {
      return (
        <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Alert severity="info">
            No chart data available. Historical data will be displayed here.
          </Alert>
        </Box>
      );
    }

    const commonProps = {
      width: '100%',
      height: 400,
      data: chartData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    };

    switch (chartType) {
      case 'area':
        return (
          <ResponsiveContainer {...commonProps}>
            <AreaChart data={chartData}>
              <defs>
                <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#667eea" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#667eea" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
              <XAxis 
                dataKey="time" 
                stroke="#b0bec5"
                fontSize={12}
              />
              <YAxis 
                stroke="#b0bec5"
                fontSize={12}
                domain={['dataMin - 10', 'dataMax + 10']}
              />
              <Tooltip content={<CustomTooltip />} />
              <Area
                type="monotone"
                dataKey="price"
                stroke="#667eea"
                strokeWidth={2}
                fillOpacity={1}
                fill="url(#colorPrice)"
              />
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'volume':
        return (
          <ResponsiveContainer {...commonProps}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
              <XAxis 
                dataKey="time" 
                stroke="#b0bec5"
                fontSize={12}
              />
              <YAxis 
                stroke="#b0bec5"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="volume" fill="#764ba2" opacity={0.8} />
            </BarChart>
          </ResponsiveContainer>
        );

      default: // line
        return (
          <ResponsiveContainer {...commonProps}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
              <XAxis 
                dataKey="time" 
                stroke="#b0bec5"
                fontSize={12}
              />
              <YAxis 
                stroke="#b0bec5"
                fontSize={12}
                domain={['dataMin - 10', 'dataMax + 10']}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="price"
                stroke="#667eea"
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: '#667eea' }}
              />
            </LineChart>
          </ResponsiveContainer>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Box>
        {/* Chart Controls */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <ToggleButtonGroup
            value={chartType}
            exclusive
            onChange={handleChartTypeChange}
            size="small"
            sx={{
              '& .MuiToggleButton-root': {
                color: 'text.secondary',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                '&.Mui-selected': {
                  backgroundColor: 'rgba(102, 126, 234, 0.2)',
                  color: 'primary.main',
                },
              },
            }}
          >
            <ToggleButton value="line">Line</ToggleButton>
            <ToggleButton value="area">Area</ToggleButton>
            <ToggleButton value="volume">Volume</ToggleButton>
          </ToggleButtonGroup>

          <ToggleButtonGroup
            value={timeframe}
            exclusive
            onChange={handleTimeframeChange}
            size="small"
            sx={{
              '& .MuiToggleButton-root': {
                color: 'text.secondary',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                '&.Mui-selected': {
                  backgroundColor: 'rgba(102, 126, 234, 0.2)',
                  color: 'primary.main',
                },
              },
            }}
          >
            <ToggleButton value="1D">1D</ToggleButton>
            <ToggleButton value="1W">1W</ToggleButton>
            <ToggleButton value="1M">1M</ToggleButton>
            <ToggleButton value="3M">3M</ToggleButton>
            <ToggleButton value="1Y">1Y</ToggleButton>
          </ToggleButtonGroup>
        </Box>

        {/* Chart */}
        <Box
          sx={{
            background: 'rgba(26, 29, 58, 0.3)',
            borderRadius: 2,
            p: 2,
            border: '1px solid rgba(255, 255, 255, 0.1)',
          }}
        >
          {renderChart()}
        </Box>

        {/* Chart Info */}
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
            {symbol} - {timeframe} Chart ({chartType.charAt(0).toUpperCase() + chartType.slice(1)})
          </Typography>
        </Box>
      </Box>
    </motion.div>
  );
};

export default StockChart;
