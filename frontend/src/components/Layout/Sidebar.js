import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Analytics as AnalyticsIcon,
  AccountBalance as PortfolioIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  Bookmark as BookmarkIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTheme as useCustomTheme } from '../../context/ThemeContext';

const drawerWidth = 280;

const menuItems = [
  {
    text: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/dashboard',
    description: 'Overview and market summary',
  },
  {
    text: 'Stock Analysis',
    icon: <AnalyticsIcon />,
    path: '/analysis',
    description: 'Detailed stock analysis',
  },
  {
    text: 'Portfolio',
    icon: <PortfolioIcon />,
    path: '/portfolio',
    description: 'Your investment portfolio',
  },
  {
    text: 'Watchlist',
    icon: <BookmarkIcon />,
    path: '/watchlist',
    description: 'Saved stocks to watch',
  },
];

const bottomMenuItems = [
  {
    text: 'Settings',
    icon: <SettingsIcon />,
    path: '/settings',
    description: 'App preferences',
  },
];

const Sidebar = ({ open, onClose }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { themeMode, actualThemeMode } = useCustomTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleNavigation = (path) => {
    navigate(path);
    if (isMobile) {
      onClose();
    }
  };

  const isActive = (path) => {
    return location.pathname === path || 
           (path === '/dashboard' && location.pathname === '/');
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box
        sx={{
          p: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '1px',
            background: 'var(--sidebar-border)',
          },
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <TrendingUpIcon sx={{ mr: 1, fontSize: 28 }} />
          <Typography variant="h6" sx={{ fontWeight: 700 }}>
            StockX
          </Typography>
        </Box>
        <Typography variant="body2" sx={{ opacity: 0.9 }}>
          Advanced Stock Analysis Platform
        </Typography>
      </Box>

      {/* Main Navigation */}
      <Box sx={{ flexGrow: 1, p: 2 }}>
        <List sx={{ p: 0 }}>
          {menuItems.map((item, index) => (
            <motion.div
              key={item.text}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <ListItem disablePadding sx={{ mb: 1 }}>
                <ListItemButton
                  onClick={() => handleNavigation(item.path)}
                  sx={{
                    borderRadius: 2,
                    py: 1.5,
                    px: 2,
                    background: isActive(item.path)
                      ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)'
                      : 'transparent',
                    border: isActive(item.path)
                      ? '1px solid rgba(102, 126, 234, 0.3)'
                      : '1px solid transparent',
                    '&:hover': {
                      background: 'rgba(102, 126, 234, 0.1)',
                      border: '1px solid rgba(102, 126, 234, 0.2)',
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                >
                  <ListItemIcon
                    sx={{
                      color: isActive(item.path) ? 'primary.main' : 'text.secondary',
                      minWidth: 40,
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: isActive(item.path) ? 600 : 400,
                          color: isActive(item.path) ? 'primary.main' : 'text.primary',
                        }}
                      >
                        {item.text}
                      </Typography>
                    }
                    secondary={
                      <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                        {item.description}
                      </Typography>
                    }
                  />
                </ListItemButton>
              </ListItem>
            </motion.div>
          ))}
        </List>
      </Box>

      {/* Bottom Section */}
      <Box sx={{ p: 2 }}>
        <Divider sx={{ mb: 2, borderColor: 'rgba(255, 255, 255, 0.1)' }} />
        
        {/* Theme Status */}
        <Box
          sx={{
            p: 2,
            mb: 2,
            borderRadius: 2,
            background: actualThemeMode === 'dark'
              ? 'rgba(102, 126, 234, 0.1)'
              : 'rgba(102, 126, 234, 0.05)',
            border: actualThemeMode === 'dark'
              ? '1px solid rgba(102, 126, 234, 0.2)'
              : '1px solid rgba(102, 126, 234, 0.15)',
            transition: 'all 0.3s ease',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: '#667eea',
                mr: 1,
              }}
            />
            <Typography variant="body2" sx={{ color: '#667eea', fontWeight: 600 }}>
              Theme: {themeMode.charAt(0).toUpperCase() + themeMode.slice(1)}
            </Typography>
          </Box>
          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
            Active: {actualThemeMode.charAt(0).toUpperCase() + actualThemeMode.slice(1)} Mode
          </Typography>
        </Box>

        {/* Market Status */}
        <Box
          sx={{
            p: 2,
            mb: 2,
            borderRadius: 2,
            background: actualThemeMode === 'dark'
              ? 'rgba(76, 175, 80, 0.1)'
              : 'rgba(76, 175, 80, 0.05)',
            border: actualThemeMode === 'dark'
              ? '1px solid rgba(76, 175, 80, 0.2)'
              : '1px solid rgba(76, 175, 80, 0.15)',
            transition: 'all 0.3s ease',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: '#4caf50',
                mr: 1,
                animation: 'pulse 2s infinite',
              }}
            />
            <Typography variant="body2" sx={{ color: '#4caf50', fontWeight: 600 }}>
              Market Open
            </Typography>
          </Box>
          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
            NSE: 9:15 AM - 3:30 PM
          </Typography>
        </Box>

        {/* Bottom Menu Items */}
        <List sx={{ p: 0 }}>
          {bottomMenuItems.map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                onClick={() => handleNavigation(item.path)}
                sx={{
                  borderRadius: 2,
                  py: 1,
                  px: 2,
                  background: isActive(item.path)
                    ? 'rgba(102, 126, 234, 0.1)'
                    : 'transparent',
                  '&:hover': {
                    background: 'rgba(102, 126, 234, 0.1)',
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive(item.path) ? 'primary.main' : 'text.secondary',
                    minWidth: 40,
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  secondary={item.description}
                  primaryTypographyProps={{
                    variant: 'body2',
                    fontWeight: isActive(item.path) ? 600 : 400,
                    color: isActive(item.path) ? 'primary.main' : 'text.primary',
                  }}
                  secondaryTypographyProps={{
                    variant: 'caption',
                    color: 'text.secondary',
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );

  return (
    <Drawer
      variant={isMobile ? 'temporary' : 'permanent'}
      open={isMobile ? open : true}
      onClose={onClose}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          background: 'var(--sidebar-bg)',
          backdropFilter: 'blur(10px)',
          border: 'none',
          borderRight: '1px solid var(--sidebar-border)',
          transition: 'all 0.3s ease',
        },
      }}
    >
      {drawerContent}
    </Drawer>
  );
};

export default Sidebar;
