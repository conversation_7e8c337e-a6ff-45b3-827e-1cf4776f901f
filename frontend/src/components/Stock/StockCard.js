import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Tooltip,
  Skeleton,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as RemoveIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { stockUtils } from '../../services/api';
import { useStock } from '../../context/StockContext';

const StockCard = ({ 
  stock, 
  data, 
  loading = false, 
  onClick, 
  showActions = true,
  compact = false 
}) => {
  const { addToWatchlist, removeFromWatchlist, isInWatchlist } = useStock();
  
  if (!stock) return null;

  const stockData = data || {};
  const lastPrice = stockData.last_price || 0;
  const ohlc = stockData.ohlc || {};
  const previousClose = ohlc.close || lastPrice;
  const change = lastPrice - previousClose;
  const changePercent = stockUtils.calculatePercentageChange(lastPrice, previousClose);
  const volume = stockData.volume || 0;
  const isPositive = change >= 0;
  const inWatchlist = isInWatchlist(stock.symbol);

  const handleWatchlistToggle = (e) => {
    e.stopPropagation();
    if (inWatchlist) {
      removeFromWatchlist(stock.symbol);
    } else {
      addToWatchlist(stock);
    }
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick(stock);
    }
  };

  if (loading) {
    return (
      <Card className="glass" sx={{ height: compact ? 120 : 160 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Skeleton variant="text" width={80} height={24} />
            <Skeleton variant="rectangular" width={40} height={20} />
          </Box>
          <Skeleton variant="text" width="60%" height={16} sx={{ mb: 2 }} />
          <Skeleton variant="text" width={100} height={32} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <Skeleton variant="text" width={60} height={16} />
            <Skeleton variant="text" width={80} height={16} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      whileHover={{ y: -4 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: 'spring', stiffness: 300 }}
    >
      <Card
        className="stock-card glass"
        onClick={handleCardClick}
        sx={{
          height: compact ? 120 : 160,
          cursor: onClick ? 'pointer' : 'default',
          position: 'relative',
          overflow: 'visible',
          '&:hover': {
            boxShadow: `0 8px 25px ${isPositive ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)'}`,
          },
        }}
      >
        <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 700, color: 'text.primary' }}>
                {stock.symbol}
              </Typography>
              {!compact && (
                <Typography
                  variant="caption"
                  sx={{
                    color: 'text.secondary',
                    display: 'block',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: 150,
                  }}
                >
                  {stock.name}
                </Typography>
              )}
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Chip
                label={stock.exchange}
                size="small"
                sx={{
                  backgroundColor: 'rgba(102, 126, 234, 0.2)',
                  color: 'primary.main',
                  fontWeight: 500,
                  fontSize: '0.7rem',
                  height: 20,
                }}
              />
              {showActions && (
                <Tooltip title={inWatchlist ? 'Remove from watchlist' : 'Add to watchlist'}>
                  <IconButton
                    size="small"
                    onClick={handleWatchlistToggle}
                    sx={{
                      color: inWatchlist ? 'warning.main' : 'text.secondary',
                      '&:hover': {
                        color: 'warning.main',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                      },
                    }}
                  >
                    {inWatchlist ? <BookmarkIcon fontSize="small" /> : <BookmarkBorderIcon fontSize="small" />}
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>

          {/* Price */}
          <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Typography
              variant={compact ? "h6" : "h5"}
              sx={{
                fontWeight: 700,
                color: 'text.primary',
                mb: 0.5,
              }}
            >
              ₹{stockUtils.formatPrice(lastPrice)}
            </Typography>

            {/* Change */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {isPositive ? (
                <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16 }} />
              ) : (
                <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16 }} />
              )}
              <Typography
                variant="body2"
                sx={{
                  color: isPositive ? 'success.main' : 'error.main',
                  fontWeight: 600,
                }}
              >
                {isPositive ? '+' : ''}₹{stockUtils.formatPrice(Math.abs(change))}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: isPositive ? 'success.main' : 'error.main',
                  fontWeight: 600,
                }}
              >
                ({stockUtils.formatPercentage(changePercent)})
              </Typography>
            </Box>
          </Box>

          {/* Footer */}
          {!compact && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                Vol: {stockUtils.formatVolume(volume)}
              </Typography>
              {ohlc.high && ohlc.low && (
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  H: ₹{stockUtils.formatPrice(ohlc.high)} L: ₹{stockUtils.formatPrice(ohlc.low)}
                </Typography>
              )}
            </Box>
          )}

          {/* Live indicator */}
          {lastPrice > 0 && (
            <Box
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: isPositive ? 'success.main' : 'error.main',
                animation: 'pulse 2s infinite',
              }}
            />
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default StockCard;
