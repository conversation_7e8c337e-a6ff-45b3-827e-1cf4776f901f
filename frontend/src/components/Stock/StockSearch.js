import React, { useState, useEffect } from 'react';
import {
  Autocomplete,
  TextField,
  Box,
  Typography,
  Chip,
  Paper,
  InputAdornment,
  CircularProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { stockUtils } from '../../services/api';
import { useStock } from '../../context/StockContext';

const StockSearch = ({ onStockSelect, placeholder = "Search stocks...", autoFocus = false }) => {
  const [searchValue, setSearchValue] = useState('');
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const { fetchStockData } = useStock();

  // Popular stocks for initial display
  const popularStocks = stockUtils.popularStocks;

  useEffect(() => {
    if (searchValue.length === 0) {
      setOptions(popularStocks);
      return;
    }

    if (searchValue.length < 2) {
      setOptions([]);
      return;
    }

    // Filter popular stocks based on search
    const filtered = popularStocks.filter(
      stock =>
        stock.symbol.toLowerCase().includes(searchValue.toLowerCase()) ||
        stock.name.toLowerCase().includes(searchValue.toLowerCase())
    );

    setOptions(filtered);
  }, [searchValue]);

  const handleStockSelect = async (event, value) => {
    if (value) {
      setLoading(true);
      try {
        if (onStockSelect) {
          onStockSelect(value);
        }
        
        // Fetch stock data
        await fetchStockData(value.symbol, value.exchange);
      } catch (error) {
        console.error('Error selecting stock:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const renderOption = (props, option) => (
    <Box component="li" {...props}>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        style={{ width: '100%' }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 1 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
              flexShrink: 0,
            }}
          >
            <TrendingUpIcon sx={{ color: 'white', fontSize: 20 }} />
          </Box>
          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
            <Typography variant="body1" sx={{ fontWeight: 600, color: 'text.primary' }}>
              {option.symbol}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {option.name}
            </Typography>
          </Box>
          <Chip
            label={option.exchange}
            size="small"
            sx={{
              backgroundColor: 'rgba(102, 126, 234, 0.2)',
              color: 'primary.main',
              fontWeight: 500,
              fontSize: '0.75rem',
            }}
          />
        </Box>
      </motion.div>
    </Box>
  );

  const renderInput = (params) => (
    <TextField
      {...params}
      placeholder={placeholder}
      autoFocus={autoFocus}
      InputProps={{
        ...params.InputProps,
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon sx={{ color: 'text.secondary' }} />
          </InputAdornment>
        ),
        endAdornment: (
          <InputAdornment position="end">
            {loading && <CircularProgress size={20} />}
            {params.InputProps.endAdornment}
          </InputAdornment>
        ),
        sx: {
          background: 'rgba(255, 255, 255, 0.05)',
          borderRadius: 2,
          '& .MuiOutlinedInput-notchedOutline': {
            border: '1px solid rgba(255, 255, 255, 0.2)',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            border: '1px solid rgba(102, 126, 234, 0.5)',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            border: '2px solid #667eea',
          },
        },
      }}
    />
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Autocomplete
        options={options}
        getOptionLabel={(option) => `${option.symbol} - ${option.name}`}
        renderOption={renderOption}
        renderInput={renderInput}
        onChange={handleStockSelect}
        onInputChange={(event, newInputValue) => {
          setSearchValue(newInputValue);
        }}
        filterOptions={(options) => options} // We handle filtering manually
        PaperComponent={({ children, ...props }) => (
          <Paper
            {...props}
            sx={{
              background: 'rgba(26, 29, 58, 0.95)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: 2,
              mt: 1,
              maxHeight: 400,
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: 8,
              },
              '&::-webkit-scrollbar-track': {
                background: 'rgba(255, 255, 255, 0.1)',
                borderRadius: 4,
              },
              '&::-webkit-scrollbar-thumb': {
                background: 'rgba(255, 255, 255, 0.3)',
                borderRadius: 4,
              },
            }}
          >
            {children}
          </Paper>
        )}
        noOptionsText={
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {searchValue.length < 2
                ? 'Type at least 2 characters to search'
                : 'No stocks found'}
            </Typography>
          </Box>
        }
        loadingText={
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <CircularProgress size={20} sx={{ mr: 1 }} />
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Searching...
            </Typography>
          </Box>
        }
        sx={{
          '& .MuiAutocomplete-listbox': {
            padding: 1,
          },
          '& .MuiAutocomplete-option': {
            borderRadius: 1,
            margin: '2px 0',
            '&:hover': {
              background: 'rgba(102, 126, 234, 0.1)',
            },
            '&.Mui-focused': {
              background: 'rgba(102, 126, 234, 0.2)',
            },
          },
        }}
      />
      
      {searchValue.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              mt: 1,
              display: 'block',
              textAlign: 'center',
            }}
          >
            Popular stocks: RELIANCE, TCS, INFY, HDFCBANK, ICICIBANK
          </Typography>
        </motion.div>
      )}
    </motion.div>
  );
};

export default StockSearch;
