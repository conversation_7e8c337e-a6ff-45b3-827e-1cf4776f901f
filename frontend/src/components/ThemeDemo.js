import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  Typo<PERSON>,
  Button,
  Chip,
  Paper,
} from '@mui/material';
import {
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  Brightness4 as AutoModeIcon,
} from '@mui/icons-material';
import { useTheme } from '../context/ThemeContext';

const ThemeDemo = () => {
  const { themeMode, actualThemeMode, toggleTheme, isDark, isLight, isAuto } = useTheme();

  const getThemeIcon = () => {
    if (isLight) return <LightModeIcon />;
    if (isDark) return <DarkModeIcon />;
    if (isAuto) return <AutoModeIcon />;
    return <DarkModeIcon />;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
        Theme Demo
      </Typography>
      
      <Card className="glass" sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            {getThemeIcon()}
            <Typography variant="h6">
              Current Theme: {themeMode.charAt(0).toUpperCase() + themeMode.slice(1)}
            </Typography>
          </Box>
          
          <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
            Active Mode: {actualThemeMode.charAt(0).toUpperCase() + actualThemeMode.slice(1)}
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <Chip 
              label="Dark" 
              color={isDark ? "primary" : "default"}
              variant={isDark ? "filled" : "outlined"}
            />
            <Chip 
              label="Light" 
              color={isLight ? "primary" : "default"}
              variant={isLight ? "filled" : "outlined"}
            />
            <Chip 
              label="Auto" 
              color={isAuto ? "primary" : "default"}
              variant={isAuto ? "filled" : "outlined"}
            />
          </Box>
          
          <Button 
            variant="contained" 
            onClick={toggleTheme}
            startIcon={getThemeIcon()}
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            }}
          >
            Toggle Theme
          </Button>
        </CardContent>
      </Card>

      <Paper className="glass" sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Theme Features
        </Typography>
        <Box component="ul" sx={{ pl: 2 }}>
          <li>
            <Typography variant="body2" sx={{ mb: 1 }}>
              ✅ Navbar adapts to theme
            </Typography>
          </li>
          <li>
            <Typography variant="body2" sx={{ mb: 1 }}>
              ✅ Sidebar adapts to theme
            </Typography>
          </li>
          <li>
            <Typography variant="body2" sx={{ mb: 1 }}>
              ✅ All cards and components adapt
            </Typography>
          </li>
          <li>
            <Typography variant="body2" sx={{ mb: 1 }}>
              ✅ Smooth transitions between themes
            </Typography>
          </li>
          <li>
            <Typography variant="body2" sx={{ mb: 1 }}>
              ✅ Auto mode follows system preference
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              ✅ Theme preference persisted in localStorage
            </Typography>
          </li>
        </Box>
      </Paper>
    </Box>
  );
};

export default ThemeDemo;
