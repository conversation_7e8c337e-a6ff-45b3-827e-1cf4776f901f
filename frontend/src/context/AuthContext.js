import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI } from '../services/authAPI';
import toast from 'react-hot-toast';

// Auth Context
const AuthContext = createContext();

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Auth Provider Component
export function AuthProvider({ children }) {
  const [state, setState] = useState(initialState);

  // Actions
  const actions = {
    setLoading: (loading) => {
      setState(prev => ({ ...prev, isLoading: loading }));
    },

    setError: (error) => {
      setState(prev => ({ ...prev, error }));
      if (error) {
        toast.error(error);
      }
    },

    clearError: () => {
      setState(prev => ({ ...prev, error: null }));
    },

    setUser: (user) => {
      setState(prev => ({
        ...prev,
        user,
        isAuthenticated: !!user,
        isLoading: false,
        error: null,
      }));
    },

    // Register new user
    register: async (userData) => {
      try {
        actions.setLoading(true);
        actions.clearError();

        const response = await authAPI.register(userData);
        
        toast.success('Registration successful! Please check your email.');
        return response;
      } catch (error) {
        const message = error.response?.data?.detail || error.message || 'Registration failed';
        actions.setError(message);
        throw error;
      } finally {
        actions.setLoading(false);
      }
    },

    // Login user
    login: async (credentials) => {
      try {
        actions.setLoading(true);
        actions.clearError();

        const response = await authAPI.login(credentials);
        
        // Store tokens
        localStorage.setItem('access_token', response.access_token);
        localStorage.setItem('refresh_token', response.refresh_token);
        
        // Get user info
        const userInfo = await authAPI.getCurrentUser();
        actions.setUser(userInfo);
        
        toast.success('Login successful!');
        return response;
      } catch (error) {
        const message = error.response?.data?.detail || error.message || 'Login failed';
        actions.setError(message);
        throw error;
      } finally {
        actions.setLoading(false);
      }
    },

    // Logout user
    logout: () => {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      actions.setUser(null);
      toast.success('Logged out successfully');
    },

    // Refresh token
    refreshToken: async () => {
      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        const response = await authAPI.refreshToken(refreshToken);
        
        // Update tokens
        localStorage.setItem('access_token', response.access_token);
        localStorage.setItem('refresh_token', response.refresh_token);
        
        return response;
      } catch (error) {
        // If refresh fails, logout user
        actions.logout();
        throw error;
      }
    },

    // Update user profile
    updateProfile: async (profileData) => {
      try {
        actions.setLoading(true);
        actions.clearError();

        const updatedProfile = await authAPI.updateProfile(profileData);
        
        // Update user in state
        setState(prev => ({
          ...prev,
          user: {
            ...prev.user,
            profile: updatedProfile,
          },
        }));
        
        toast.success('Profile updated successfully!');
        return updatedProfile;
      } catch (error) {
        const message = error.response?.data?.detail || error.message || 'Profile update failed';
        actions.setError(message);
        throw error;
      } finally {
        actions.setLoading(false);
      }
    },

    // Forgot password
    forgotPassword: async (email) => {
      try {
        actions.setLoading(true);
        actions.clearError();

        await authAPI.forgotPassword(email);
        toast.success('Password reset link sent to your email!');
      } catch (error) {
        const message = error.response?.data?.detail || error.message || 'Failed to send reset email';
        actions.setError(message);
        throw error;
      } finally {
        actions.setLoading(false);
      }
    },

    // Reset password
    resetPassword: async (token, newPassword) => {
      try {
        actions.setLoading(true);
        actions.clearError();

        await authAPI.resetPassword(token, newPassword);
        toast.success('Password reset successfully! Please login with your new password.');
      } catch (error) {
        const message = error.response?.data?.detail || error.message || 'Password reset failed';
        actions.setError(message);
        throw error;
      } finally {
        actions.setLoading(false);
      }
    },

    // Change password
    changePassword: async (currentPassword, newPassword) => {
      try {
        actions.setLoading(true);
        actions.clearError();

        await authAPI.changePassword(currentPassword, newPassword);
        toast.success('Password changed successfully!');
      } catch (error) {
        const message = error.response?.data?.detail || error.message || 'Password change failed';
        actions.setError(message);
        throw error;
      } finally {
        actions.setLoading(false);
      }
    },

    // Check authentication status
    checkAuth: async () => {
      try {
        const token = localStorage.getItem('access_token');
        if (!token) {
          actions.setUser(null);
          return;
        }

        // Try to get current user
        const userInfo = await authAPI.getCurrentUser();
        actions.setUser(userInfo);
      } catch (error) {
        // If token is invalid, try to refresh
        try {
          await actions.refreshToken();
          const userInfo = await authAPI.getCurrentUser();
          actions.setUser(userInfo);
        } catch (refreshError) {
          // If refresh fails, logout
          actions.logout();
        }
      }
    },
  };

  // Check authentication on mount
  useEffect(() => {
    actions.checkAuth();
  }, []);

  // Auto-refresh token before expiry
  useEffect(() => {
    if (state.isAuthenticated) {
      const interval = setInterval(() => {
        const token = localStorage.getItem('access_token');
        if (token) {
          // Check if token is close to expiry (refresh 5 minutes before)
          try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const expiryTime = payload.exp * 1000;
            const currentTime = Date.now();
            const timeUntilExpiry = expiryTime - currentTime;
            
            // Refresh if token expires in less than 5 minutes
            if (timeUntilExpiry < 5 * 60 * 1000) {
              actions.refreshToken().catch(() => {
                // If refresh fails, logout
                actions.logout();
              });
            }
          } catch (error) {
            // If token parsing fails, logout
            actions.logout();
          }
        }
      }, 60000); // Check every minute

      return () => clearInterval(interval);
    }
  }, [state.isAuthenticated]);

  const value = {
    ...state,
    ...actions,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
