import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { stockAPI } from '../services/api';
import toast from 'react-hot-toast';

// Initial state
const initialState = {
  watchlist: JSON.parse(localStorage.getItem('stockx-watchlist') || '[]'),
  selectedStock: null,
  stockData: {},
  taskStatus: {},
  loading: false,
  error: null,
};

// Action types
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_SELECTED_STOCK: 'SET_SELECTED_STOCK',
  SET_STOCK_DATA: 'SET_STOCK_DATA',
  SET_TASK_STATUS: 'SET_TASK_STATUS',
  ADD_TO_WATCHLIST: 'ADD_TO_WATCHLIST',
  REMOVE_FROM_WATCHLIST: 'REMOVE_FROM_WATCHLIST',
  UPDATE_WATCHLIST: 'UPDATE_WATCHLIST',
  CLEAR_ERROR: 'CLEAR_ERROR',
};

// Reducer
function stockReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
    
    case ActionTypes.CLEAR_ERROR:
      return { ...state, error: null };
    
    case ActionTypes.SET_SELECTED_STOCK:
      return { ...state, selectedStock: action.payload };
    
    case ActionTypes.SET_STOCK_DATA:
      return {
        ...state,
        stockData: {
          ...state.stockData,
          [action.payload.symbol]: action.payload.data,
        },
      };
    
    case ActionTypes.SET_TASK_STATUS:
      return {
        ...state,
        taskStatus: {
          ...state.taskStatus,
          [action.payload.taskId]: action.payload.status,
        },
      };
    
    case ActionTypes.ADD_TO_WATCHLIST:
      const newWatchlist = [...state.watchlist, action.payload];
      localStorage.setItem('stockx-watchlist', JSON.stringify(newWatchlist));
      return { ...state, watchlist: newWatchlist };
    
    case ActionTypes.REMOVE_FROM_WATCHLIST:
      const filteredWatchlist = state.watchlist.filter(
        stock => stock.symbol !== action.payload
      );
      localStorage.setItem('stockx-watchlist', JSON.stringify(filteredWatchlist));
      return { ...state, watchlist: filteredWatchlist };
    
    case ActionTypes.UPDATE_WATCHLIST:
      localStorage.setItem('stockx-watchlist', JSON.stringify(action.payload));
      return { ...state, watchlist: action.payload };
    
    default:
      return state;
  }
}

// Context
const StockContext = createContext();

// Provider component
export function StockProvider({ children }) {
  const [state, dispatch] = useReducer(stockReducer, initialState);

  // Actions
  const actions = {
    setLoading: (loading) => {
      dispatch({ type: ActionTypes.SET_LOADING, payload: loading });
    },

    setError: (error) => {
      dispatch({ type: ActionTypes.SET_ERROR, payload: error });
      if (error) {
        toast.error(error);
      }
    },

    clearError: () => {
      dispatch({ type: ActionTypes.CLEAR_ERROR });
    },

    setSelectedStock: (stock) => {
      dispatch({ type: ActionTypes.SET_SELECTED_STOCK, payload: stock });
    },

    // Fetch stock data
    fetchStockData: async (symbol, exchange = 'NSE') => {
      try {
        actions.setLoading(true);
        actions.clearError();

        const response = await stockAPI.requestStockData(symbol, exchange);
        const taskId = response.task_id;

        // Poll for task completion
        const pollTask = async () => {
          try {
            const taskResult = await stockAPI.getTaskStatus(taskId);
            
            dispatch({
              type: ActionTypes.SET_TASK_STATUS,
              payload: { taskId, status: taskResult },
            });

            if (taskResult.status === 'SUCCESS' && taskResult.result) {
              dispatch({
                type: ActionTypes.SET_STOCK_DATA,
                payload: { symbol, data: taskResult.result },
              });
              actions.setLoading(false);
              toast.success(`Stock data loaded for ${symbol}`);
              return taskResult.result;
            } else if (taskResult.status === 'FAILURE') {
              actions.setError(taskResult.error || 'Failed to fetch stock data');
              return null;
            } else {
              // Continue polling
              setTimeout(pollTask, 2000);
            }
          } catch (error) {
            actions.setError('Failed to check task status');
            return null;
          }
        };

        return await pollTask();
      } catch (error) {
        actions.setError(error.message || 'Failed to fetch stock data');
        return null;
      }
    },

    // Fetch LTP
    fetchLTP: async (symbol, exchange = 'NSE') => {
      try {
        const response = await stockAPI.requestLTP(symbol, exchange);
        const taskId = response.task_id;

        // Poll for task completion
        const pollTask = async () => {
          try {
            const taskResult = await stockAPI.getTaskStatus(taskId);
            
            if (taskResult.status === 'SUCCESS' && taskResult.result) {
              return taskResult.result;
            } else if (taskResult.status === 'FAILURE') {
              throw new Error(taskResult.error || 'Failed to fetch LTP');
            } else {
              // Continue polling
              await new Promise(resolve => setTimeout(resolve, 1000));
              return await pollTask();
            }
          } catch (error) {
            throw error;
          }
        };

        return await pollTask();
      } catch (error) {
        actions.setError(error.message || 'Failed to fetch LTP');
        return null;
      }
    },

    // Watchlist management
    addToWatchlist: (stock) => {
      const exists = state.watchlist.find(s => s.symbol === stock.symbol);
      if (!exists) {
        dispatch({ type: ActionTypes.ADD_TO_WATCHLIST, payload: stock });
        toast.success(`${stock.symbol} added to watchlist`);
      } else {
        toast.info(`${stock.symbol} is already in watchlist`);
      }
    },

    removeFromWatchlist: (symbol) => {
      dispatch({ type: ActionTypes.REMOVE_FROM_WATCHLIST, payload: symbol });
      toast.success(`${symbol} removed from watchlist`);
    },

    updateWatchlist: (watchlist) => {
      dispatch({ type: ActionTypes.UPDATE_WATCHLIST, payload: watchlist });
    },

    // Check if stock is in watchlist
    isInWatchlist: (symbol) => {
      return state.watchlist.some(stock => stock.symbol === symbol);
    },
  };

  // Auto-refresh watchlist data
  useEffect(() => {
    const refreshWatchlist = async () => {
      if (state.watchlist.length > 0) {
        for (const stock of state.watchlist) {
          try {
            const ltp = await actions.fetchLTP(stock.symbol, stock.exchange);
            if (ltp) {
              // Update stock data with latest LTP
              dispatch({
                type: ActionTypes.SET_STOCK_DATA,
                payload: { 
                  symbol: stock.symbol, 
                  data: { ...state.stockData[stock.symbol], ...ltp } 
                },
              });
            }
          } catch (error) {
            console.error(`Failed to refresh ${stock.symbol}:`, error);
          }
        }
      }
    };

    // Refresh every 30 seconds
    const interval = setInterval(refreshWatchlist, 30000);
    return () => clearInterval(interval);
  }, [state.watchlist]);

  const value = {
    ...state,
    ...actions,
  };

  return (
    <StockContext.Provider value={value}>
      {children}
    </StockContext.Provider>
  );
}

// Hook to use the context
export function useStock() {
  const context = useContext(StockContext);
  if (!context) {
    throw new Error('useStock must be used within a StockProvider');
  }
  return context;
}
