/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1d3a 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

/* Utility classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.shimmer {
  background: linear-gradient(to right, #1a1d3a 8%, #2a2d4a 18%, #1a1d3a 33%);
  background-size: 800px 104px;
  animation: shimmer 1.5s infinite linear;
}

/* Glass morphism effect */
.glass {
  background: rgba(26, 29, 58, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #1a1d3a 25%, #2a2d4a 50%, #1a1d3a 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

/* Status indicators */
.status-positive {
  color: #4caf50;
}

.status-negative {
  color: #f44336;
}

.status-neutral {
  color: #b0bec5;
}

/* Responsive design */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

/* Custom Material-UI overrides */
.MuiCard-root {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.MuiCard-root:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.MuiButton-root {
  transition: all 0.2s ease-in-out;
}

.MuiChip-root {
  backdrop-filter: blur(10px);
}

/* Chart container */
.chart-container {
  background: rgba(26, 29, 58, 0.6);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Stock card hover effects */
.stock-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.stock-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3);
}

/* Price change animations */
.price-up {
  color: #4caf50;
  animation: pulse 0.5s ease-in-out;
}

.price-down {
  color: #f44336;
  animation: pulse 0.5s ease-in-out;
}

/* Search input styling */
.search-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}
