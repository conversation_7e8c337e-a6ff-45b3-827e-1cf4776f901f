import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Paper,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Analytics as AnalyticsIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

// Components
import StockSearch from '../components/Stock/StockSearch';
import StockCard from '../components/Stock/StockCard';

// Context and services
import { useStock } from '../context/StockContext';
import { stockUtils } from '../services/api';

const Dashboard = () => {
  const navigate = useNavigate();
  const { 
    watchlist, 
    stockData, 
    loading, 
    fetchStockData, 
    setSelectedStock 
  } = useStock();

  const [marketStats, setMarketStats] = useState({
    totalStocks: watchlist.length,
    gainers: 0,
    losers: 0,
    unchanged: 0,
  });

  // Calculate market stats
  useEffect(() => {
    let gainers = 0;
    let losers = 0;
    let unchanged = 0;

    watchlist.forEach(stock => {
      const data = stockData[stock.symbol];
      if (data && data.ohlc) {
        const change = data.last_price - data.ohlc.close;
        if (change > 0) gainers++;
        else if (change < 0) losers++;
        else unchanged++;
      }
    });

    setMarketStats({
      totalStocks: watchlist.length,
      gainers,
      losers,
      unchanged,
    });
  }, [watchlist, stockData]);

  const handleStockSelect = (stock) => {
    setSelectedStock(stock);
    navigate(`/analysis/${stock.symbol}`);
  };

  const handleRefreshAll = async () => {
    for (const stock of watchlist) {
      await fetchStockData(stock.symbol, stock.exchange);
    }
  };

  const StatCard = ({ title, value, icon, color, subtitle }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="glass" sx={{ height: '100%' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color, mb: 0.5 }}>
                {value}
              </Typography>
              <Typography variant="body1" sx={{ color: 'text.primary', fontWeight: 500 }}>
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  {subtitle}
                </Typography>
              )}
            </Box>
            <Box
              sx={{
                width: 48,
                height: 48,
                borderRadius: 2,
                background: `linear-gradient(135deg, ${color}20, ${color}40)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {icon}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'text.primary' }}>
            Dashboard
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.secondary' }}>
            Monitor your stocks and market overview
          </Typography>
        </Box>
      </motion.div>

      {/* Market Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Stocks"
            value={marketStats.totalStocks}
            icon={<AnalyticsIcon sx={{ color: '#667eea', fontSize: 24 }} />}
            color="#667eea"
            subtitle="In watchlist"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Gainers"
            value={marketStats.gainers}
            icon={<TrendingUpIcon sx={{ color: '#4caf50', fontSize: 24 }} />}
            color="#4caf50"
            subtitle="Stocks up today"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Losers"
            value={marketStats.losers}
            icon={<TrendingDownIcon sx={{ color: '#f44336', fontSize: 24 }} />}
            color="#f44336"
            subtitle="Stocks down today"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Unchanged"
            value={marketStats.unchanged}
            icon={<AnalyticsIcon sx={{ color: '#ff9800', fontSize: 24 }} />}
            color="#ff9800"
            subtitle="No change"
          />
        </Grid>
      </Grid>

      {/* Search Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Paper
          className="glass"
          sx={{
            p: 3,
            mb: 4,
            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
          }}
        >
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Search Stocks
          </Typography>
          <StockSearch
            onStockSelect={handleStockSelect}
            placeholder="Search for stocks to analyze..."
          />
        </Paper>
      </motion.div>

      {/* Watchlist */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            Watchlist
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh all stocks">
              <IconButton
                onClick={handleRefreshAll}
                disabled={loading}
                sx={{
                  background: 'rgba(102, 126, 234, 0.1)',
                  '&:hover': { background: 'rgba(102, 126, 234, 0.2)' },
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/analysis')}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                },
              }}
            >
              Add Stock
            </Button>
          </Box>
        </Box>

        {watchlist.length === 0 ? (
          <Paper
            className="glass"
            sx={{
              p: 6,
              textAlign: 'center',
              background: 'rgba(26, 29, 58, 0.3)',
            }}
          >
            <AnalyticsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1, color: 'text.primary' }}>
              Your watchlist is empty
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
              Search and add stocks to start monitoring their performance
            </Typography>
            <Button
              variant="contained"
              onClick={() => navigate('/analysis')}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              }}
            >
              Explore Stocks
            </Button>
          </Paper>
        ) : (
          <Grid container spacing={3}>
            {watchlist.map((stock, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={stock.symbol}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <StockCard
                    stock={stock}
                    data={stockData[stock.symbol]}
                    loading={loading}
                    onClick={handleStockSelect}
                  />
                </motion.div>
              </Grid>
            ))}
          </Grid>
        )}
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Quick Actions
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<AnalyticsIcon />}
                onClick={() => navigate('/analysis')}
                sx={{
                  py: 2,
                  borderColor: 'rgba(102, 126, 234, 0.5)',
                  color: 'primary.main',
                  '&:hover': {
                    borderColor: 'primary.main',
                    background: 'rgba(102, 126, 234, 0.1)',
                  },
                }}
              >
                Stock Analysis
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<TrendingUpIcon />}
                onClick={() => navigate('/portfolio')}
                sx={{
                  py: 2,
                  borderColor: 'rgba(76, 175, 80, 0.5)',
                  color: 'success.main',
                  '&:hover': {
                    borderColor: 'success.main',
                    background: 'rgba(76, 175, 80, 0.1)',
                  },
                }}
              >
                Portfolio
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefreshAll}
                disabled={loading || watchlist.length === 0}
                sx={{
                  py: 2,
                  borderColor: 'rgba(255, 152, 0, 0.5)',
                  color: 'warning.main',
                  '&:hover': {
                    borderColor: 'warning.main',
                    background: 'rgba(255, 152, 0, 0.1)',
                  },
                }}
              >
                Refresh Data
              </Button>
            </Grid>
          </Grid>
        </Box>
      </motion.div>
    </Box>
  );
};

export default Dashboard;
