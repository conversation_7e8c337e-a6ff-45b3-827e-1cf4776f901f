import React from 'react';
import { Box, Container } from '@mui/material';
import { motion } from 'framer-motion';
import LoginForm from '../components/Auth/LoginForm';

const Login = () => {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 4,
      }}
    >
      <Container maxWidth="sm">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
        >
          <LoginForm />
        </motion.div>
      </Container>
    </Box>
  );
};

export default Login;
