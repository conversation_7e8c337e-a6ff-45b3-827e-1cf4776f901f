import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Paper,
  Alert,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  TrendingUp as TrendingUpIcon,
  AccountBalance as AccountBalanceIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const Portfolio = () => {
  const navigate = useNavigate();
  const [portfolioData] = useState([]); // This would come from API/context in real app

  const StatCard = ({ title, value, subtitle, color, icon }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="glass" sx={{ height: '100%' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700, color, mb: 0.5 }}>
                {value}
              </Typography>
              <Typography variant="body1" sx={{ color: 'text.primary', fontWeight: 500 }}>
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  {subtitle}
                </Typography>
              )}
            </Box>
            <Box
              sx={{
                width: 48,
                height: 48,
                borderRadius: 2,
                background: `linear-gradient(135deg, ${color}20, ${color}40)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {icon}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'text.primary' }}>
            Portfolio
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.secondary' }}>
            Track your investments and portfolio performance
          </Typography>
        </Box>
      </motion.div>

      {/* Portfolio Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Value"
            value="₹0"
            subtitle="Current portfolio value"
            color="#667eea"
            icon={<AccountBalanceIcon sx={{ color: '#667eea', fontSize: 24 }} />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Gain/Loss"
            value="₹0"
            subtitle="Overall P&L"
            color="#4caf50"
            icon={<TrendingUpIcon sx={{ color: '#4caf50', fontSize: 24 }} />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Holdings"
            value="0"
            subtitle="Number of stocks"
            color="#ff9800"
            icon={<AssessmentIcon sx={{ color: '#ff9800', fontSize: 24 }} />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Day's Change"
            value="₹0"
            subtitle="Today's P&L"
            color="#f44336"
            icon={<TrendingUpIcon sx={{ color: '#f44336', fontSize: 24 }} />}
          />
        </Grid>
      </Grid>

      {/* Portfolio Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {portfolioData.length === 0 ? (
          <Paper
            className="glass"
            sx={{
              p: 6,
              textAlign: 'center',
              background: 'rgba(26, 29, 58, 0.3)',
            }}
          >
            <AccountBalanceIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1, color: 'text.primary' }}>
              Your portfolio is empty
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
              Start building your portfolio by adding your stock holdings
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/analysis')}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                mr: 2,
              }}
            >
              Add Holdings
            </Button>
            <Button
              variant="outlined"
              onClick={() => navigate('/dashboard')}
              sx={{
                borderColor: 'rgba(102, 126, 234, 0.5)',
                color: 'primary.main',
              }}
            >
              Go to Dashboard
            </Button>
          </Paper>
        ) : (
          <Grid container spacing={3}>
            {/* Portfolio holdings would be displayed here */}
          </Grid>
        )}
      </motion.div>

      {/* Coming Soon Alert */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Alert 
          severity="info" 
          sx={{ 
            mt: 4,
            backgroundColor: 'rgba(33, 150, 243, 0.1)',
            border: '1px solid rgba(33, 150, 243, 0.2)',
          }}
        >
          <Typography variant="body1" sx={{ fontWeight: 600, mb: 1 }}>
            Portfolio Management Coming Soon!
          </Typography>
          <Typography variant="body2">
            We're working on advanced portfolio management features including:
          </Typography>
          <Box component="ul" sx={{ mt: 1, pl: 2 }}>
            <li>Add and track your stock holdings</li>
            <li>Real-time portfolio valuation</li>
            <li>Profit & Loss tracking</li>
            <li>Portfolio performance analytics</li>
            <li>Asset allocation charts</li>
            <li>Dividend tracking</li>
          </Box>
        </Alert>
      </motion.div>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
          },
        }}
        onClick={() => navigate('/analysis')}
      >
        <AddIcon />
      </Fab>
    </Box>
  );
};

export default Portfolio;
