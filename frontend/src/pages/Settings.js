import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Switch,
  FormControlLabel,
  Button,
  Divider,
  Alert,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Palette as PaletteIcon,
  Security as SecurityIcon,
  Info as InfoIcon,
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  Brightness4 as Brightness4Icon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useTheme } from '../context/ThemeContext';

const Settings = () => {
  const { themeMode, setTheme } = useTheme();

  const [settings, setSettings] = useState({
    notifications: {
      priceAlerts: true,
      marketNews: false,
      portfolioUpdates: true,
      emailNotifications: false,
    },
    display: {
      currency: 'INR',
      refreshInterval: 30,
      compactView: false,
    },
    trading: {
      defaultExchange: 'NSE',
      confirmOrders: true,
      showAdvancedCharts: true,
    },
  });

  const handleSettingChange = (category, setting, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value,
      },
    }));
  };

  const SettingsCard = ({ title, icon, children }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="glass" sx={{ height: '100%' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: 2,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
              }}
            >
              {icon}
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {title}
            </Typography>
          </Box>
          {children}
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'text.primary' }}>
            Settings
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.secondary' }}>
            Customize your StockX experience
          </Typography>
        </Box>
      </motion.div>

      <Grid container spacing={3}>
        {/* Notifications Settings */}
        <Grid item xs={12} md={6}>
          <SettingsCard
            title="Notifications"
            icon={<NotificationsIcon sx={{ color: 'white', fontSize: 20 }} />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.priceAlerts}
                    onChange={(e) => handleSettingChange('notifications', 'priceAlerts', e.target.checked)}
                    color="primary"
                  />
                }
                label="Price Alerts"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.marketNews}
                    onChange={(e) => handleSettingChange('notifications', 'marketNews', e.target.checked)}
                    color="primary"
                  />
                }
                label="Market News"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.portfolioUpdates}
                    onChange={(e) => handleSettingChange('notifications', 'portfolioUpdates', e.target.checked)}
                    color="primary"
                  />
                }
                label="Portfolio Updates"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.emailNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'emailNotifications', e.target.checked)}
                    color="primary"
                  />
                }
                label="Email Notifications"
              />
            </Box>
          </SettingsCard>
        </Grid>

        {/* Display Settings */}
        <Grid item xs={12} md={6}>
          <SettingsCard
            title="Display"
            icon={<PaletteIcon sx={{ color: 'white', fontSize: 20 }} />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Theme</InputLabel>
                <Select
                  value={themeMode}
                  label="Theme"
                  onChange={(e) => setTheme(e.target.value)}
                >
                  <MenuItem value="dark">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <DarkModeIcon fontSize="small" />
                      Dark
                    </Box>
                  </MenuItem>
                  <MenuItem value="light">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LightModeIcon fontSize="small" />
                      Light
                    </Box>
                  </MenuItem>
                  <MenuItem value="auto">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Brightness4Icon fontSize="small" />
                      Auto (System)
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>

              <FormControl fullWidth size="small">
                <InputLabel>Currency</InputLabel>
                <Select
                  value={settings.display.currency}
                  label="Currency"
                  onChange={(e) => handleSettingChange('display', 'currency', e.target.value)}
                >
                  <MenuItem value="INR">INR (₹)</MenuItem>
                  <MenuItem value="USD">USD ($)</MenuItem>
                  <MenuItem value="EUR">EUR (€)</MenuItem>
                </Select>
              </FormControl>

              <TextField
                label="Refresh Interval (seconds)"
                type="number"
                size="small"
                value={settings.display.refreshInterval}
                onChange={(e) => handleSettingChange('display', 'refreshInterval', parseInt(e.target.value))}
                inputProps={{ min: 5, max: 300 }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={settings.display.compactView}
                    onChange={(e) => handleSettingChange('display', 'compactView', e.target.checked)}
                    color="primary"
                  />
                }
                label="Compact View"
              />
            </Box>
          </SettingsCard>
        </Grid>

        {/* Trading Settings */}
        <Grid item xs={12} md={6}>
          <SettingsCard
            title="Trading"
            icon={<SecurityIcon sx={{ color: 'white', fontSize: 20 }} />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Default Exchange</InputLabel>
                <Select
                  value={settings.trading.defaultExchange}
                  label="Default Exchange"
                  onChange={(e) => handleSettingChange('trading', 'defaultExchange', e.target.value)}
                >
                  <MenuItem value="NSE">NSE</MenuItem>
                  <MenuItem value="BSE">BSE</MenuItem>
                  <MenuItem value="NFO">NFO</MenuItem>
                  <MenuItem value="BFO">BFO</MenuItem>
                </Select>
              </FormControl>

              <FormControlLabel
                control={
                  <Switch
                    checked={settings.trading.confirmOrders}
                    onChange={(e) => handleSettingChange('trading', 'confirmOrders', e.target.checked)}
                    color="primary"
                  />
                }
                label="Confirm Orders"
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={settings.trading.showAdvancedCharts}
                    onChange={(e) => handleSettingChange('trading', 'showAdvancedCharts', e.target.checked)}
                    color="primary"
                  />
                }
                label="Advanced Charts"
              />
            </Box>
          </SettingsCard>
        </Grid>

        {/* App Information */}
        <Grid item xs={12} md={6}>
          <SettingsCard
            title="App Information"
            icon={<InfoIcon sx={{ color: 'white', fontSize: 20 }} />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  Version
                </Typography>
                <Chip label="1.0.0" size="small" color="primary" />
              </Box>
              <Divider />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  API Status
                </Typography>
                <Chip label="Connected" size="small" color="success" />
              </Box>
              <Divider />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  Last Updated
                </Typography>
                <Typography variant="body2">
                  {new Date().toLocaleDateString()}
                </Typography>
              </Box>
              <Divider />
              <Button
                variant="outlined"
                size="small"
                sx={{
                  borderColor: 'rgba(102, 126, 234, 0.5)',
                  color: 'primary.main',
                }}
              >
                Check for Updates
              </Button>
            </Box>
          </SettingsCard>
        </Grid>

        {/* Save Settings */}
        <Grid item xs={12}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
              <Button
                variant="contained"
                size="large"
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  px: 4,
                }}
              >
                Save Settings
              </Button>
              <Button
                variant="outlined"
                size="large"
                sx={{
                  borderColor: 'rgba(102, 126, 234, 0.5)',
                  color: 'primary.main',
                  px: 4,
                }}
              >
                Reset to Default
              </Button>
            </Box>
          </motion.div>
        </Grid>

        {/* Beta Notice */}
        <Grid item xs={12}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Alert 
              severity="info"
              sx={{
                backgroundColor: 'rgba(33, 150, 243, 0.1)',
                border: '1px solid rgba(33, 150, 243, 0.2)',
              }}
            >
              <Typography variant="body1" sx={{ fontWeight: 600, mb: 1 }}>
                Beta Version Notice
              </Typography>
              <Typography variant="body2">
                StockX is currently in beta. Some features may be limited or under development. 
                Your feedback helps us improve the platform. Settings are saved locally and may be reset during updates.
              </Typography>
            </Alert>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;
