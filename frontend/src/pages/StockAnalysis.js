import React, { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Paper,
  Chip,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Timeline as TimelineIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useParams, useNavigate } from 'react-router-dom';

// Components
import StockSearch from '../components/Stock/StockSearch';
import StockChart from '../components/Charts/StockChart';

// Context and services
import { useStock } from '../context/StockContext';
import { stockUtils } from '../services/api';

const StockAnalysis = () => {
  const { symbol } = useParams();
  const navigate = useNavigate();
  const {
    selectedStock,
    stockData,
    loading,
    error,
    fetchStockData,
    setSelectedStock,
    addToWatchlist,
    removeFromWatchlist,
    isInWatchlist,
  } = useStock();

  const [analysisData, setAnalysisData] = useState(null);

  // Load stock data if symbol is provided in URL
  useEffect(() => {
    if (symbol && (!selectedStock || selectedStock.symbol !== symbol)) {
      const stock = { symbol: symbol.toUpperCase(), exchange: 'NSE' };
      setSelectedStock(stock);
      fetchStockData(symbol, 'NSE');
    }
  }, [symbol]);

  // Get current stock data
  const currentData = selectedStock ? stockData[selectedStock.symbol] : null;
  const inWatchlist = selectedStock ? isInWatchlist(selectedStock.symbol) : false;

  const handleStockSelect = (stock) => {
    setSelectedStock(stock);
    navigate(`/analysis/${stock.symbol}`);
  };

  const handleWatchlistToggle = () => {
    if (!selectedStock) return;
    
    if (inWatchlist) {
      removeFromWatchlist(selectedStock.symbol);
    } else {
      addToWatchlist(selectedStock);
    }
  };

  const handleRefresh = () => {
    if (selectedStock) {
      fetchStockData(selectedStock.symbol, selectedStock.exchange);
    }
  };

  const MetricCard = ({ title, value, subtitle, color = 'text.primary', icon }) => (
    <Card className="glass" sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 500 }}>
            {title}
          </Typography>
          {icon}
        </Box>
        <Typography variant="h6" sx={{ color, fontWeight: 700, mb: 0.5 }}>
          {value}
        </Typography>
        {subtitle && (
          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, color: 'text.primary' }}>
            Stock Analysis
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.secondary' }}>
            Detailed analysis and real-time data for stocks
          </Typography>
        </Box>
      </motion.div>

      {/* Search Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Paper className="glass" sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Search Stock
          </Typography>
          <StockSearch
            onStockSelect={handleStockSelect}
            placeholder="Enter stock symbol or name..."
            autoFocus={!selectedStock}
          />
        </Paper>
      </motion.div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        </motion.div>
      )}

      {/* Loading State */}
      {loading && selectedStock && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Paper className="glass" sx={{ p: 4, textAlign: 'center', mb: 4 }}>
            <CircularProgress sx={{ mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1 }}>
              Loading {selectedStock.symbol} data...
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Fetching real-time market data
            </Typography>
          </Paper>
        </motion.div>
      )}

      {/* Stock Analysis Content */}
      {selectedStock && currentData && !loading && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* Stock Header */}
          <Paper className="glass" sx={{ p: 3, mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary' }}>
                    {selectedStock.symbol}
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                    {selectedStock.name || 'Stock Analysis'}
                  </Typography>
                </Box>
                <Chip
                  label={selectedStock.exchange}
                  sx={{
                    backgroundColor: 'rgba(102, 126, 234, 0.2)',
                    color: 'primary.main',
                    fontWeight: 600,
                  }}
                />
              </Box>
              
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={handleRefresh}
                  disabled={loading}
                >
                  Refresh
                </Button>
                <Button
                  variant={inWatchlist ? "contained" : "outlined"}
                  startIcon={inWatchlist ? <BookmarkIcon /> : <BookmarkBorderIcon />}
                  onClick={handleWatchlistToggle}
                  sx={{
                    ...(inWatchlist && {
                      background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
                    }),
                  }}
                >
                  {inWatchlist ? 'In Watchlist' : 'Add to Watchlist'}
                </Button>
              </Box>
            </Box>

            {/* Price Information */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
              <Typography variant="h3" sx={{ fontWeight: 700, color: 'text.primary' }}>
                ₹{stockUtils.formatPrice(currentData.last_price)}
              </Typography>
              
              {currentData.ohlc && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {currentData.last_price >= currentData.ohlc.close ? (
                    <TrendingUpIcon sx={{ color: 'success.main' }} />
                  ) : (
                    <TrendingDownIcon sx={{ color: 'error.main' }} />
                  )}
                  <Typography
                    variant="h6"
                    sx={{
                      color: currentData.last_price >= currentData.ohlc.close ? 'success.main' : 'error.main',
                      fontWeight: 600,
                    }}
                  >
                    {currentData.last_price >= currentData.ohlc.close ? '+' : ''}
                    ₹{stockUtils.formatPrice(Math.abs(currentData.last_price - currentData.ohlc.close))}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: currentData.last_price >= currentData.ohlc.close ? 'success.main' : 'error.main',
                      fontWeight: 600,
                    }}
                  >
                    ({stockUtils.formatPercentage(
                      stockUtils.calculatePercentageChange(currentData.last_price, currentData.ohlc.close)
                    )})
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>

          {/* Metrics Grid */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {currentData.ohlc && (
              <>
                <Grid item xs={6} sm={3}>
                  <MetricCard
                    title="Open"
                    value={`₹${stockUtils.formatPrice(currentData.ohlc.open)}`}
                    icon={<TimelineIcon sx={{ color: 'info.main', fontSize: 20 }} />}
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <MetricCard
                    title="High"
                    value={`₹${stockUtils.formatPrice(currentData.ohlc.high)}`}
                    color="success.main"
                    icon={<TrendingUpIcon sx={{ color: 'success.main', fontSize: 20 }} />}
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <MetricCard
                    title="Low"
                    value={`₹${stockUtils.formatPrice(currentData.ohlc.low)}`}
                    color="error.main"
                    icon={<TrendingDownIcon sx={{ color: 'error.main', fontSize: 20 }} />}
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <MetricCard
                    title="Previous Close"
                    value={`₹${stockUtils.formatPrice(currentData.ohlc.close)}`}
                    icon={<TimelineIcon sx={{ color: 'warning.main', fontSize: 20 }} />}
                  />
                </Grid>
              </>
            )}
            
            {currentData.volume && (
              <Grid item xs={6} sm={3}>
                <MetricCard
                  title="Volume"
                  value={stockUtils.formatVolume(currentData.volume)}
                  subtitle="Shares traded"
                />
              </Grid>
            )}
            
            {currentData.average_price && (
              <Grid item xs={6} sm={3}>
                <MetricCard
                  title="Avg Price"
                  value={`₹${stockUtils.formatPrice(currentData.average_price)}`}
                  subtitle="Today's average"
                />
              </Grid>
            )}
          </Grid>

          {/* Chart Section */}
          <Paper className="glass" sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
              Price Chart
            </Typography>
            <StockChart stockData={currentData} symbol={selectedStock.symbol} />
          </Paper>

          {/* Additional Analysis */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper className="glass" sx={{ p: 3, height: '100%' }}>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Market Summary
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Last Trade Time
                    </Typography>
                    <Typography variant="body2">
                      {currentData.last_trade_time ? 
                        new Date(currentData.last_trade_time).toLocaleTimeString() : 
                        'N/A'
                      }
                    </Typography>
                  </Box>
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Last Quantity
                    </Typography>
                    <Typography variant="body2">
                      {currentData.last_quantity || 'N/A'}
                    </Typography>
                  </Box>
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Buy Quantity
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'success.main' }}>
                      {stockUtils.formatVolume(currentData.buy_quantity || 0)}
                    </Typography>
                  </Box>
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      Sell Quantity
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'error.main' }}>
                      {stockUtils.formatVolume(currentData.sell_quantity || 0)}
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Paper className="glass" sx={{ p: 3, height: '100%' }}>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Technical Indicators
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Alert severity="info" sx={{ backgroundColor: 'rgba(33, 150, 243, 0.1)' }}>
                    Technical analysis features coming soon!
                  </Alert>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    • Moving Averages (SMA, EMA)
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    • RSI (Relative Strength Index)
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    • MACD (Moving Average Convergence Divergence)
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    • Bollinger Bands
                  </Typography>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </motion.div>
      )}

      {/* No Stock Selected */}
      {!selectedStock && !loading && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Paper
            className="glass"
            sx={{
              p: 6,
              textAlign: 'center',
              background: 'rgba(26, 29, 58, 0.3)',
            }}
          >
            <TimelineIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1, color: 'text.primary' }}>
              Select a stock to analyze
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              Use the search above to find and analyze any stock
            </Typography>
          </Paper>
        </motion.div>
      )}
    </Box>
  );
};

export default StockAnalysis;
