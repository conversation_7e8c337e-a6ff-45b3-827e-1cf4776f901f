import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers here if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    const message = error.response?.data?.detail || error.message || 'An error occurred';
    return Promise.reject(new Error(message));
  }
);

// Stock API endpoints
export const stockAPI = {
  // Request stock data
  requestStockData: async (symbol, exchange = 'NSE') => {
    const response = await api.get('/api/v1/get_stock_data', {
      params: { symbol, exchange },
    });
    return response;
  },

  // Request LTP
  requestLTP: async (symbol, exchange = 'NSE') => {
    const response = await api.get('/api/v1/get_stock_ltp', {
      params: { symbol, exchange },
    });
    return response;
  },

  // Get task status
  getTaskStatus: async (taskId) => {
    const response = await api.get(`/api/v1/task_status/${taskId}`);
    return response;
  },

  // Health check
  healthCheck: async () => {
    const response = await api.get('/api/v1/health');
    return response;
  },

  // Get supported exchanges
  getExchanges: async () => {
    const response = await api.get('/api/v1/exchanges');
    return response;
  },

  // Basic health endpoint
  getHealth: async () => {
    const response = await api.get('/health');
    return response;
  },
};

// Utility functions
export const apiUtils = {
  // Format error message
  formatError: (error) => {
    if (error.response?.data?.detail) {
      return error.response.data.detail;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  },

  // Check if API is available
  checkAPIHealth: async () => {
    try {
      await stockAPI.getHealth();
      return true;
    } catch (error) {
      return false;
    }
  },

  // Retry function with exponential backoff
  retryRequest: async (requestFn, maxRetries = 3, baseDelay = 1000) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        if (attempt === maxRetries) {
          throw error;
        }
        
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  },
};

// Stock data utilities
export const stockUtils = {
  // Calculate percentage change
  calculatePercentageChange: (current, previous) => {
    if (!previous || previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  },

  // Format price
  formatPrice: (price, decimals = 2) => {
    if (typeof price !== 'number') return '0.00';
    return price.toFixed(decimals);
  },

  // Format percentage
  formatPercentage: (percentage, decimals = 2) => {
    if (typeof percentage !== 'number') return '0.00%';
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(decimals)}%`;
  },

  // Format volume
  formatVolume: (volume) => {
    if (!volume) return '0';
    
    if (volume >= 10000000) {
      return `${(volume / 10000000).toFixed(1)}Cr`;
    } else if (volume >= 100000) {
      return `${(volume / 100000).toFixed(1)}L`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    
    return volume.toString();
  },

  // Get price change color
  getPriceChangeColor: (change) => {
    if (change > 0) return 'success.main';
    if (change < 0) return 'error.main';
    return 'text.secondary';
  },

  // Validate stock symbol
  validateSymbol: (symbol) => {
    if (!symbol || typeof symbol !== 'string') return false;
    return /^[A-Z0-9&-]{1,20}$/.test(symbol.trim().toUpperCase());
  },

  // Popular Indian stocks for suggestions
  popularStocks: [
    { symbol: 'RELIANCE', name: 'Reliance Industries Ltd', exchange: 'NSE' },
    { symbol: 'TCS', name: 'Tata Consultancy Services Ltd', exchange: 'NSE' },
    { symbol: 'INFY', name: 'Infosys Ltd', exchange: 'NSE' },
    { symbol: 'HDFCBANK', name: 'HDFC Bank Ltd', exchange: 'NSE' },
    { symbol: 'ICICIBANK', name: 'ICICI Bank Ltd', exchange: 'NSE' },
    { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Ltd', exchange: 'NSE' },
    { symbol: 'ITC', name: 'ITC Ltd', exchange: 'NSE' },
    { symbol: 'SBIN', name: 'State Bank of India', exchange: 'NSE' },
    { symbol: 'BHARTIARTL', name: 'Bharti Airtel Ltd', exchange: 'NSE' },
    { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank Ltd', exchange: 'NSE' },
    { symbol: 'LT', name: 'Larsen & Toubro Ltd', exchange: 'NSE' },
    { symbol: 'ASIANPAINT', name: 'Asian Paints Ltd', exchange: 'NSE' },
    { symbol: 'MARUTI', name: 'Maruti Suzuki India Ltd', exchange: 'NSE' },
    { symbol: 'TITAN', name: 'Titan Company Ltd', exchange: 'NSE' },
    { symbol: 'WIPRO', name: 'Wipro Ltd', exchange: 'NSE' },
  ],
};

export default api;
