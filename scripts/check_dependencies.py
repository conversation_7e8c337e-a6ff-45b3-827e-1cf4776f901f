#!/usr/bin/env python3
"""Check Python dependencies for compatibility."""

import subprocess
import sys
import pkg_resources
from packaging import version

def check_dependency_conflicts():
    """Check for dependency conflicts."""
    print("🔍 Checking Python dependencies...")
    
    try:
        # Read requirements
        with open('requirements.txt', 'r') as f:
            requirements = f.read().splitlines()
        
        # Filter out comments and empty lines
        requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]
        
        print(f"📦 Found {len(requirements)} requirements")
        
        # Check for known conflicts
        conflicts = []
        
        for req in requirements:
            if '==' in req:
                package, version_spec = req.split('==')
                
                # Check SQLAlchemy and databases conflict
                if package == 'sqlalchemy' and version.parse(version_spec) >= version.parse('1.5.0'):
                    if any('databases' in r for r in requirements):
                        conflicts.append(f"SQLAlchemy {version_spec} conflicts with databases package (requires <1.5)")
                
                # Check other potential conflicts
                if package == 'pydantic' and version.parse(version_spec) >= version.parse('2.0.0'):
                    if any('fastapi' in r and '0.68' in r for r in requirements):
                        conflicts.append(f"Pydantic {version_spec} may conflict with older FastAPI versions")
        
        if conflicts:
            print("❌ Dependency conflicts found:")
            for conflict in conflicts:
                print(f"   • {conflict}")
            return False
        else:
            print("✅ No obvious dependency conflicts found")
            return True
            
    except Exception as e:
        print(f"❌ Error checking dependencies: {e}")
        return False

def test_pip_install():
    """Test if pip can resolve dependencies."""
    print("\n🧪 Testing pip dependency resolution...")
    
    try:
        # Run pip check
        result = subprocess.run([sys.executable, '-m', 'pip', 'check'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Pip check passed")
            return True
        else:
            print("❌ Pip check failed:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running pip check: {e}")
        return False

def suggest_fixes():
    """Suggest fixes for common issues."""
    print("\n💡 Suggested fixes:")
    print("1. Use SQLAlchemy 1.4.x with databases package:")
    print("   sqlalchemy==1.4.53")
    print("   databases[postgresql]==0.8.0")
    print("")
    print("2. Or use SQLAlchemy 2.0+ with asyncpg directly:")
    print("   sqlalchemy[asyncio]==2.0.23")
    print("   asyncpg==0.29.0")
    print("   (Remove databases package)")
    print("")
    print("3. Update requirements.txt with compatible versions")
    print("4. Rebuild Docker images: docker-compose build --no-cache")

def main():
    """Main function."""
    print("🔧 StockX Dependency Checker")
    print("=" * 40)
    
    conflicts_ok = check_dependency_conflicts()
    pip_ok = test_pip_install()
    
    print("\n" + "=" * 40)
    print("📊 Results:")
    print(f"   Conflicts: {'✅ PASS' if conflicts_ok else '❌ FAIL'}")
    print(f"   Pip Check: {'✅ PASS' if pip_ok else '❌ FAIL'}")
    
    if not conflicts_ok or not pip_ok:
        suggest_fixes()
        return False
    else:
        print("\n🎉 Dependencies look good!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
