-- Development data for StockX
-- This script adds sample data for development and testing

-- Note: This will only run if tables exist (after migrations)
-- The script is designed to be idempotent (safe to run multiple times)

DO $$
BEGIN
    -- Check if users table exists before inserting data
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'users') THEN
        
        -- Insert sample user if not exists
        INSERT INTO users (id, email, password_hash, is_verified, is_active, created_at, updated_at)
        SELECT 
            gen_random_uuid(),
            '<EMAIL>',
            '$2b$12$LQv3c1yqBwEHxPuNYjNOyOeRq19Whahayt/YuwNFkouuiIfqFcjmu', -- password: Demo123!
            true,
            true,
            NOW(),
            NOW()
        WHERE NOT EXISTS (
            SELECT 1 FROM users WHERE email = '<EMAIL>'
        );
        
        -- Insert sample profile for demo user
        INSERT INTO profiles (id, user_id, full_name, mobile_number, preferences, created_at, updated_at)
        SELECT 
            gen_random_uuid(),
            u.id,
            'Demo User',
            '+1234567890',
            '{"theme": "dark", "notifications": true, "defaultExchange": "NSE"}',
            NOW(),
            NOW()
        FROM users u
        WHERE u.email = '<EMAIL>'
        AND NOT EXISTS (
            SELECT 1 FROM profiles p WHERE p.user_id = u.id
        );
        
        RAISE NOTICE 'Development data inserted successfully';
        RAISE NOTICE 'Demo user: <EMAIL> / Demo123!';
        
    ELSE
        RAISE NOTICE 'Users table not found, skipping development data insertion';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error inserting development data: %', SQLERRM;
END $$;
