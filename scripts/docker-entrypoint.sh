#!/bin/bash

# Docker entrypoint script for StockX backend services
# Handles database migrations and service startup

set -e

echo "🐳 StockX Docker Entrypoint"
echo "=========================="

# Function to wait for database
wait_for_db() {
    echo "⏳ Waiting for PostgreSQL to be ready..."
    
    # Extract database connection details from DATABASE_URL
    # Format: postgresql://user:password@host:port/database
    if [ -n "$DATABASE_URL" ]; then
        # Parse DATABASE_URL
        DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
        DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
        DB_USER=$(echo $DATABASE_URL | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')
        DB_NAME=$(echo $DATABASE_URL | sed -n 's/.*\/\([^?]*\).*/\1/p')
        
        # Default values if parsing fails
        DB_HOST=${DB_HOST:-postgres}
        DB_PORT=${DB_PORT:-5432}
        DB_USER=${DB_USER:-stockx}
        DB_NAME=${DB_NAME:-stockx}
        
        echo "📡 Connecting to: $DB_HOST:$DB_PORT/$DB_NAME as $DB_USER"
        
        # Wait for PostgreSQL to be ready
        until pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME"; do
            echo "⏳ PostgreSQL is unavailable - sleeping..."
            sleep 2
        done
        
        echo "✅ PostgreSQL is ready!"
    else
        echo "⚠️  DATABASE_URL not set, skipping database check"
    fi
}

# Function to run database migrations
run_migrations() {
    echo "🔄 Running database migrations..."

    # Check if alembic.ini exists
    if [ -f "alembic.ini" ]; then
        # Initialize alembic if migrations directory doesn't exist
        if [ ! -d "migrations" ]; then
            echo "📝 Initializing Alembic..."
            alembic init migrations
        fi

        # Check if migrations/versions directory exists
        if [ ! -d "migrations/versions" ]; then
            echo "📝 Creating initial migration..."
            alembic revision --autogenerate -m "Initial migration with auth tables"
        fi

        # Run migrations
        echo "⬆️  Applying database migrations..."
        alembic upgrade head
        echo "✅ Database migrations completed"
    else
        echo "⚠️  alembic.ini not found, skipping migrations"
        echo "🔧 Creating tables directly..."
        python -c "
from app.database import create_tables
try:
    create_tables()
    print('✅ Tables created successfully')
except Exception as e:
    print(f'❌ Table creation failed: {e}')
"
    fi
}

# Function to wait for Redis
wait_for_redis() {
    if [ -n "$REDIS_URL" ]; then
        echo "⏳ Waiting for Redis to be ready..."
        
        # Extract Redis host from URL
        REDIS_HOST=$(echo $REDIS_URL | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')
        REDIS_HOST=${REDIS_HOST:-redis}
        
        # Wait for Redis
        until redis-cli -h "$REDIS_HOST" ping > /dev/null 2>&1; do
            echo "⏳ Redis is unavailable - sleeping..."
            sleep 2
        done
        
        echo "✅ Redis is ready!"
    else
        echo "⚠️  REDIS_URL not set, skipping Redis check"
    fi
}

# Main execution logic
main() {
    echo "🚀 Starting StockX service: $@"
    
    # Determine service type based on command
    case "$1" in
        "uvicorn")
            echo "🌐 Starting API server..."
            wait_for_db
            wait_for_redis
            run_migrations
            ;;
        "celery")
            if [[ "$2" == *"worker"* ]]; then
                echo "👷 Starting Celery worker..."
                wait_for_db
                wait_for_redis
            elif [[ "$2" == *"flower"* ]]; then
                echo "🌸 Starting Flower monitoring..."
                wait_for_redis
            fi
            ;;
        *)
            echo "🔧 Starting custom command: $@"
            ;;
    esac
    
    echo "✅ Pre-startup checks completed"
    echo "🎯 Executing: $@"
    echo ""
    
    # Execute the main command
    exec "$@"
}

# Handle signals for graceful shutdown
trap 'echo "🛑 Received shutdown signal"; exit 0' SIGTERM SIGINT

# Run main function with all arguments
main "$@"
