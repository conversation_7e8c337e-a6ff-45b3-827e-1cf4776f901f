#!/bin/bash

# Docker management script for StockX
# Provides easy commands for managing the Docker environment

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is available
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker daemon is not running"
        exit 1
    fi
}

# Show usage
show_usage() {
    echo "🐳 StockX Docker Manager"
    echo "======================="
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  start           Start all services"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  status          Show service status"
    echo "  logs [service]  Show logs (optionally for specific service)"
    echo "  shell <service> Open shell in service container"
    echo "  build           Build all images"
    echo "  clean           Clean up containers and images"
    echo "  reset           Reset everything (clean + rebuild)"
    echo "  db-shell        Open PostgreSQL shell"
    echo "  redis-cli       Open Redis CLI"
    echo "  migrate         Run database migrations"
    echo "  test            Run tests"
    echo "  prod            Start in production mode"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start all services"
    echo "  $0 logs api                 # Show API logs"
    echo "  $0 shell api                # Open shell in API container"
    echo "  $0 db-shell                 # Open PostgreSQL shell"
    echo "  $0 prod                     # Start in production mode"
}

# Start services
start_services() {
    log_info "Starting StockX services..."
    
    # Check if .env exists
    if [ ! -f "$PROJECT_DIR/.env" ]; then
        log_warning ".env file not found, copying from .env.example"
        cp "$PROJECT_DIR/.env.example" "$PROJECT_DIR/.env"
        log_warning "Please edit .env file with your configuration"
    fi
    
    cd "$PROJECT_DIR"
    
    # Start core services first
    log_info "Starting core services (PostgreSQL + Redis)..."
    docker-compose up -d postgres redis
    
    # Wait for core services
    log_info "Waiting for core services to be ready..."
    sleep 10
    
    # Start backend services
    log_info "Starting backend services..."
    docker-compose up -d api worker
    
    # Start frontend
    log_info "Starting frontend..."
    docker-compose up -d frontend
    
    log_success "All services started!"
    show_urls
}

# Stop services
stop_services() {
    log_info "Stopping StockX services..."
    cd "$PROJECT_DIR"
    docker-compose down
    log_success "All services stopped!"
}

# Restart services
restart_services() {
    log_info "Restarting StockX services..."
    stop_services
    start_services
}

# Show service status
show_status() {
    log_info "Service Status:"
    cd "$PROJECT_DIR"
    docker-compose ps
}

# Show logs
show_logs() {
    cd "$PROJECT_DIR"
    if [ -n "$1" ]; then
        log_info "Showing logs for $1..."
        docker-compose logs -f "$1"
    else
        log_info "Showing logs for all services..."
        docker-compose logs -f
    fi
}

# Open shell in service
open_shell() {
    if [ -z "$1" ]; then
        log_error "Please specify a service name"
        exit 1
    fi
    
    cd "$PROJECT_DIR"
    log_info "Opening shell in $1..."
    docker-compose exec "$1" bash
}

# Build images
build_images() {
    log_info "Building Docker images..."
    cd "$PROJECT_DIR"
    docker-compose build
    log_success "Images built successfully!"
}

# Clean up
clean_up() {
    log_info "Cleaning up Docker resources..."
    cd "$PROJECT_DIR"
    
    # Stop and remove containers
    docker-compose down -v
    
    # Remove images
    docker-compose down --rmi all
    
    # Clean up unused resources
    docker system prune -f
    
    log_success "Cleanup completed!"
}

# Reset everything
reset_all() {
    log_warning "This will remove all containers, images, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        clean_up
        build_images
        start_services
    else
        log_info "Reset cancelled"
    fi
}

# Open database shell
db_shell() {
    log_info "Opening PostgreSQL shell..."
    cd "$PROJECT_DIR"
    docker-compose exec postgres psql -U stockx -d stockx
}

# Open Redis CLI
redis_cli() {
    log_info "Opening Redis CLI..."
    cd "$PROJECT_DIR"
    docker-compose exec redis redis-cli
}

# Run migrations
run_migrations() {
    log_info "Running database migrations..."
    cd "$PROJECT_DIR"
    docker-compose exec api alembic upgrade head
    log_success "Migrations completed!"
}

# Run tests
run_tests() {
    log_info "Running tests..."
    cd "$PROJECT_DIR"
    docker-compose exec api python -m pytest
}

# Start in production mode
start_production() {
    log_info "Starting in production mode..."
    cd "$PROJECT_DIR"
    
    if [ ! -f ".env.prod" ]; then
        log_error "Production environment file (.env.prod) not found"
        exit 1
    fi
    
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    log_success "Production services started!"
}

# Show application URLs
show_urls() {
    echo ""
    log_success "StockX is running!"
    echo ""
    echo "🌐 Application URLs:"
    echo "   Frontend:             http://localhost:3000"
    echo "   Backend API:          http://localhost:8000"
    echo "   API Documentation:    http://localhost:8000/docs"
    echo "   Authentication:       http://localhost:8000/api/v1/auth/"
    echo ""
    echo "🗄️  Database Access:"
    echo "   PostgreSQL:           localhost:5432"
    echo "   Redis:                localhost:6379"
    echo ""
    echo "🔧 Management:"
    echo "   View status:          $0 status"
    echo "   View logs:            $0 logs"
    echo "   Stop services:        $0 stop"
}

# Main execution
main() {
    check_docker
    
    case "${1:-}" in
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "shell")
            open_shell "$2"
            ;;
        "build")
            build_images
            ;;
        "clean")
            clean_up
            ;;
        "reset")
            reset_all
            ;;
        "db-shell")
            db_shell
            ;;
        "redis-cli")
            redis_cli
            ;;
        "migrate")
            run_migrations
            ;;
        "test")
            run_tests
            ;;
        "prod")
            start_production
            ;;
        *)
            show_usage
            ;;
    esac
}

# Run main function with all arguments
main "$@"
