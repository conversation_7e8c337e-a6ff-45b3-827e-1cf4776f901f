-- Database initialization script for StockX
-- This script runs when PostgreSQL container starts for the first time

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create database if not exists (this is handled by POSTGRES_DB env var)
-- But we can add any additional setup here

-- Set timezone
SET timezone = 'UTC';

-- Create indexes for better performance (will be created by Alembic migrations)
-- This is just a placeholder for any additional database setup

-- Log initialization
DO $$
BEGIN
    RAISE NOTICE 'StockX database initialized successfully';
END $$;
