#!/usr/bin/env python3
"""Initialize database tables for StockX."""

import sys
import os
import asyncio

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def init_database():
    """Initialize database tables."""
    try:
        print("🗄️  Initializing database...")
        
        # Import after path setup
        from app.database import engine, Base, database
        from app.models.auth import User, UserProfile, PasswordResetToken
        
        print("📦 Creating tables...")
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        print("✅ Database tables created successfully!")
        
        # Test connection
        print("🔗 Testing database connection...")
        await database.connect()
        
        # Test query
        result = await database.fetch_one("SELECT 1 as test")
        if result and result['test'] == 1:
            print("✅ Database connection test successful!")
        
        await database.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(init_database())
    sys.exit(0 if success else 1)
