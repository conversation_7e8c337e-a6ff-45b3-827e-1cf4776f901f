#!/bin/bash

# Quick start script for StockX with error checking

set -e

echo "🚀 StockX Quick Start"
echo "===================="

# Check if .env exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration"
fi

# Stop any existing services
echo "🛑 Stopping existing services..."
docker-compose down > /dev/null 2>&1 || true

# Build images
echo "🔨 Building Docker images..."
docker-compose build

# Start core services first
echo "🗄️  Starting PostgreSQL and Redis..."
docker-compose up -d postgres redis

# Wait for database
echo "⏳ Waiting for database to be ready..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose exec -T postgres pg_isready -U stockx -d stockx > /dev/null 2>&1; then
        echo "✅ Database is ready"
        break
    fi
    echo "⏳ Waiting for database... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    echo "❌ Database failed to start"
    docker-compose logs postgres
    exit 1
fi

# Start backend services
echo "🔧 Starting backend services..."
docker-compose up -d api worker

# Wait for API
echo "⏳ Waiting for API to be ready..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ API is ready"
        break
    fi
    echo "⏳ Waiting for API... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    echo "❌ API failed to start"
    echo "API Logs:"
    docker-compose logs api
    exit 1
fi

# Test API
echo "🧪 Testing API..."
echo "Root endpoint:"
curl -s http://localhost:8000/ | jq . 2>/dev/null || curl -s http://localhost:8000/

echo ""
echo "Health endpoint:"
curl -s http://localhost:8000/health | jq . 2>/dev/null || curl -s http://localhost:8000/health

echo ""
echo "Auth endpoints:"
curl -s http://localhost:8000/docs > /dev/null && echo "✅ API docs accessible" || echo "❌ API docs not accessible"

# Start frontend
echo ""
echo "🎨 Starting frontend..."
docker-compose up -d frontend

echo ""
echo "🎉 Services started successfully!"
echo ""
echo "🌐 Access URLs:"
echo "   Frontend:     http://localhost:3000"
echo "   Backend API:  http://localhost:8000"
echo "   API Docs:     http://localhost:8000/docs"
echo "   Health:       http://localhost:8000/health"
echo ""
echo "🔧 Management:"
echo "   View logs:    docker-compose logs -f"
echo "   Stop:         docker-compose down"
echo "   Status:       docker-compose ps"

# Show current status
echo ""
echo "📊 Current Status:"
docker-compose ps
