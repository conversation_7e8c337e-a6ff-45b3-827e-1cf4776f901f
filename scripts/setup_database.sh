#!/bin/bash

# Database setup script for StockX authentication system

set -e

echo "🗄️  Setting up StockX Database"
echo "=============================="

# Database configuration
DB_NAME="stockx"
DB_USER="stockx"
DB_PASSWORD="stockx"
DB_HOST="localhost"
DB_PORT="5432"

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL is not installed. Please install PostgreSQL first."
    echo "   macOS: brew install postgresql"
    echo "   Ubuntu: sudo apt-get install postgresql postgresql-contrib"
    echo "   Windows: Download from https://www.postgresql.org/download/"
    exit 1
fi

echo "✅ PostgreSQL detected"

# Check if PostgreSQL is running
if ! pg_isready -h $DB_HOST -p $DB_PORT &> /dev/null; then
    echo "❌ PostgreSQL is not running. Please start PostgreSQL first."
    echo "   macOS: brew services start postgresql"
    echo "   Ubuntu: sudo systemctl start postgresql"
    echo "   Manual: pg_ctl start"
    exit 1
fi

echo "✅ PostgreSQL is running"

# Create database and user
echo "📦 Creating database and user..."

# Connect as postgres user and create database/user
sudo -u postgres psql << EOF
-- Create user if not exists
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '$DB_USER') THEN
        CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';
    END IF;
END
\$\$;

-- Create database if not exists
SELECT 'CREATE DATABASE $DB_NAME OWNER $DB_USER'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$DB_NAME')\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
ALTER USER $DB_USER CREATEDB;

\q
EOF

echo "✅ Database and user created successfully"

# Test connection
echo "🔗 Testing database connection..."
if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    exit 1
fi

# Install Python dependencies if not already installed
echo "📦 Installing Python dependencies..."
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    echo "✅ Python dependencies installed"
else
    echo "⚠️  requirements.txt not found. Please run this script from the project root."
fi

# Run database migrations
echo "🔄 Running database migrations..."
if [ -f "alembic.ini" ]; then
    # Initialize alembic if not already done
    if [ ! -d "migrations/versions" ]; then
        echo "📝 Initializing database migrations..."
        alembic revision --autogenerate -m "Initial migration with auth tables"
    fi
    
    # Run migrations
    alembic upgrade head
    echo "✅ Database migrations completed"
else
    echo "⚠️  alembic.ini not found. Please run this script from the project root."
fi

echo ""
echo "🎉 Database setup completed successfully!"
echo ""
echo "📋 Database Information:"
echo "   Host: $DB_HOST"
echo "   Port: $DB_PORT"
echo "   Database: $DB_NAME"
echo "   Username: $DB_USER"
echo "   Password: $DB_PASSWORD"
echo ""
echo "🔗 Connection URL:"
echo "   postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
echo ""
echo "📝 Next Steps:"
echo "   1. Update your .env file with the database URL"
echo "   2. Configure email settings for password reset"
echo "   3. Start the application with: uvicorn app.main:app --reload"
echo ""
echo "🧪 Test the setup:"
echo "   curl http://localhost:8000/api/v1/auth/register -X POST \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"email\":\"<EMAIL>\",\"password\":\"Test123!\",\"full_name\":\"Test User\"}'"
