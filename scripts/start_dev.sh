#!/bin/bash

# Development startup script for StockX

set -e

echo "🚀 Starting StockX Development Environment"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example"
    cp .env.example .env
    echo "📝 Please edit .env file with your Kite API credentials"
    exit 1
fi

# Check if Redis is running
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis is not running. Please start Redis server first."
    echo "   Run: redis-server"
    exit 1
fi

echo "✅ Redis is running"

# Install dependencies if needed
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python -m venv venv
fi

echo "📦 Activating virtual environment..."
source venv/bin/activate

echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Start Celery worker in background
echo "🔄 Starting Celery worker..."
celery -A celery_app worker --loglevel=info --detach

# Start FastAPI application
echo "🌐 Starting FastAPI application..."
echo "📖 API Documentation: http://localhost:8000/docs"
echo "🌸 Flower (Celery monitoring): Run 'celery -A celery_app flower' in another terminal"

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
