#!/bin/bash

# Docker-based full-stack startup script for StockX with Authentication

set -e

echo "🐳 Starting StockX Full-Stack with <PERSON><PERSON>"
echo "========================================="

# Check if Docker and Docker Compose are available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "   Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker and Docker Compose detected"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example"
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration:"
    echo "   • Kite API credentials"
    echo "   • Secret key for JWT"
    echo "   • Email configuration"
    echo ""
    echo "💡 Note: Database URL will be automatically configured for Docker"
    read -p "Press Enter to continue after editing .env file..."
fi

echo "✅ Environment configuration found"

# Check if Docker daemon is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker daemon is not running. Please start Docker first."
    exit 1
fi

echo "✅ Docker daemon is running"

# Function to show service status
show_status() {
    echo ""
    echo "📊 Service Status:"
    docker-compose ps
}

# Function to show logs
show_logs() {
    echo ""
    echo "📋 Recent Logs:"
    docker-compose logs --tail=10
}

# Function to cleanup
cleanup() {
    echo ""
    echo "🧹 Cleaning up..."
    docker-compose down
    echo "✅ Services stopped"
}

# Trap cleanup on exit
trap cleanup EXIT

# Pull latest images
echo "📦 Pulling latest Docker images..."
docker-compose pull

# Build services
echo "🔨 Building services..."
docker-compose build

# Start core services first (database and redis)
echo "🗄️  Starting core services (PostgreSQL + Redis)..."
docker-compose up -d postgres redis

# Wait for core services to be healthy
echo "⏳ Waiting for core services to be ready..."
timeout=60
counter=0

while [ $counter -lt $timeout ]; do
    if docker-compose ps postgres | grep -q "healthy" && docker-compose ps redis | grep -q "healthy"; then
        echo "✅ Core services are healthy"
        break
    fi
    
    echo "⏳ Waiting for services to be healthy... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    echo "❌ Core services failed to start within $timeout seconds"
    show_logs
    exit 1
fi

# Start backend services
echo "🔧 Starting backend services (API + Worker)..."
docker-compose up -d api worker

# Wait for backend to be ready
echo "⏳ Waiting for backend API to be ready..."
timeout=60
counter=0

while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ Backend API is ready"
        break
    fi
    
    echo "⏳ Waiting for API to be ready... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    echo "❌ Backend API failed to start within $timeout seconds"
    show_logs
    exit 1
fi

# Start frontend
echo "🎨 Starting frontend..."
docker-compose up -d frontend

# Wait for frontend to be ready
echo "⏳ Waiting for frontend to be ready..."
timeout=60
counter=0

while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Frontend is ready"
        break
    fi
    
    echo "⏳ Waiting for frontend to be ready... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    echo "❌ Frontend failed to start within $timeout seconds"
    show_logs
    exit 1
fi

# Show final status
echo ""
echo "🎉 StockX Full-Stack Started Successfully!"
echo "========================================"
echo ""
echo "🌐 Application URLs:"
echo "   Frontend (React):     http://localhost:3000"
echo "   Backend API:          http://localhost:8000"
echo "   API Documentation:    http://localhost:8000/docs"
echo "   Authentication:       http://localhost:8000/api/v1/auth/"
echo ""
echo "🗄️  Database Services:"
echo "   PostgreSQL:           localhost:5432"
echo "   Redis:                localhost:6379"
echo ""
echo "🔐 Authentication Features:"
echo "   • User Registration & Login"
echo "   • JWT Token Authentication"
echo "   • Password Reset via Email"
echo "   • User Profile Management"
echo "   • Protected Routes"
echo ""
echo "🧪 Quick Test:"
echo "   1. Visit http://localhost:3000"
echo "   2. Click 'Sign Up' to create an account"
echo "   3. Login with your credentials"
echo "   4. Access protected stock analysis features"
echo ""
echo "📊 Monitoring:"
echo "   • Service Status: docker-compose ps"
echo "   • Logs: docker-compose logs -f [service]"
echo "   • Stop: docker-compose down"
echo ""

# Optional: Start monitoring services
read -p "🌸 Start Flower monitoring? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌸 Starting Flower monitoring..."
    docker-compose --profile monitoring up -d flower
    echo "   Flower Dashboard:     http://localhost:5555"
fi

# Show current status
show_status

echo ""
echo "🔧 Management Commands:"
echo "   View logs:            docker-compose logs -f"
echo "   Stop services:        docker-compose down"
echo "   Restart service:      docker-compose restart [service]"
echo "   Shell access:         docker-compose exec api bash"
echo "   Database shell:       docker-compose exec postgres psql -U stockx -d stockx"
echo ""
echo "Press Ctrl+C to stop all services..."

# Keep script running and show logs
docker-compose logs -f
