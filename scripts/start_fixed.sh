#!/bin/bash

# Fixed startup script for StockX with dependency resolution

set -e

echo "🔧 StockX Fixed Startup"
echo "======================"

# Check if .env exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration"
fi

# Check dependencies
echo "🔍 Checking dependencies..."
if command -v python3 &> /dev/null; then
    python3 scripts/check_dependencies.py || echo "⚠️  Dependency issues detected, but continuing..."
fi

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down > /dev/null 2>&1 || true

# Remove any problematic volumes
echo "🗑️  Removing old volumes..."
docker volume rm stockx_postgres_data > /dev/null 2>&1 || true

# Build with no cache to ensure fresh dependencies
echo "🔨 Building Docker images (no cache)..."
docker-compose build --no-cache

# Start PostgreSQL first
echo "🗄️  Starting PostgreSQL..."
docker-compose up -d postgres

# Wait for PostgreSQL
echo "⏳ Waiting for PostgreSQL to be ready..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose exec -T postgres pg_isready -U stockx -d stockx > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready"
        break
    fi
    echo "⏳ Waiting for PostgreSQL... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

if [ $counter -ge $timeout ]; then
    echo "❌ PostgreSQL failed to start"
    echo "PostgreSQL logs:"
    docker-compose logs postgres
    exit 1
fi

# Initialize database
echo "📦 Initializing database..."
docker-compose exec -T postgres psql -U stockx -d stockx -c "SELECT 1;" > /dev/null 2>&1 && echo "✅ Database connection verified"

# Start Redis
echo "🔴 Starting Redis..."
docker-compose up -d redis

# Wait for Redis
echo "⏳ Waiting for Redis to be ready..."
timeout=30
counter=0
while [ $counter -lt $timeout ]; do
    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis is ready"
        break
    fi
    echo "⏳ Waiting for Redis... ($counter/$timeout)"
    sleep 2
    counter=$((counter + 2))
done

# Start API
echo "🌐 Starting API..."
docker-compose up -d api

# Wait for API
echo "⏳ Waiting for API to be ready..."
timeout=120
counter=0
while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ API is ready"
        break
    fi
    
    # Show API logs if it's taking too long
    if [ $counter -gt 30 ] && [ $((counter % 20)) -eq 0 ]; then
        echo "📋 API logs (last 10 lines):"
        docker-compose logs --tail=10 api
    fi
    
    echo "⏳ Waiting for API... ($counter/$timeout)"
    sleep 3
    counter=$((counter + 3))
done

if [ $counter -ge $timeout ]; then
    echo "❌ API failed to start"
    echo "API logs:"
    docker-compose logs api
    echo ""
    echo "🔧 Troubleshooting suggestions:"
    echo "1. Check dependency conflicts: python3 scripts/check_dependencies.py"
    echo "2. Check database connection: docker-compose exec postgres psql -U stockx -d stockx"
    echo "3. Rebuild with: docker-compose build --no-cache"
    exit 1
fi

# Test API endpoints
echo "🧪 Testing API endpoints..."
echo "Root endpoint:"
curl -s http://localhost:8000/ | head -c 200
echo ""

echo "Health endpoint:"
curl -s http://localhost:8000/health | head -c 200
echo ""

# Start worker
echo "👷 Starting Celery worker..."
docker-compose up -d worker

# Start frontend
echo "🎨 Starting frontend..."
docker-compose up -d frontend

# Wait for frontend
echo "⏳ Waiting for frontend to be ready..."
timeout=120
counter=0
while [ $counter -lt $timeout ]; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ Frontend is ready"
        break
    fi
    
    # Show frontend logs if it's taking too long
    if [ $counter -gt 60 ] && [ $((counter % 30)) -eq 0 ]; then
        echo "📋 Frontend logs (last 10 lines):"
        docker-compose logs --tail=10 frontend
    fi
    
    echo "⏳ Waiting for frontend... ($counter/$timeout)"
    sleep 3
    counter=$((counter + 3))
done

if [ $counter -ge $timeout ]; then
    echo "❌ Frontend failed to start"
    echo "Frontend logs:"
    docker-compose logs frontend
    echo ""
    echo "🔧 Troubleshooting suggestions:"
    echo "1. Check for build errors in logs above"
    echo "2. Rebuild frontend: docker-compose build frontend --no-cache"
    echo "3. Check authAPI.js for syntax errors"
else
    echo "✅ Frontend is ready"
fi

# Final status
echo ""
echo "🎉 StockX Started Successfully!"
echo "=============================="
echo ""
echo "🌐 Access URLs:"
echo "   Frontend:     http://localhost:3000"
echo "   Backend API:  http://localhost:8000"
echo "   API Docs:     http://localhost:8000/docs"
echo "   Health:       http://localhost:8000/health"
echo ""
echo "🗄️  Database:"
echo "   PostgreSQL:   localhost:5432 (stockx/stockx)"
echo "   Redis:        localhost:6379"
echo ""
echo "🔐 Demo Account:"
echo "   Email:        <EMAIL>"
echo "   Password:     Demo123!"
echo ""
echo "🔧 Management:"
echo "   Status:       docker-compose ps"
echo "   Logs:         docker-compose logs -f [service]"
echo "   Stop:         docker-compose down"
echo "   Shell:        docker-compose exec [service] bash"
echo ""

# Show final status
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🧪 Quick Tests:"
echo "Backend Health:"
curl -s http://localhost:8000/health | head -c 100 || echo "❌ Backend not responding"
echo ""
echo "Frontend:"
curl -s http://localhost:3000 | head -c 100 || echo "❌ Frontend not responding"
echo ""

echo "✅ Startup complete! Check the URLs above to access your application."
