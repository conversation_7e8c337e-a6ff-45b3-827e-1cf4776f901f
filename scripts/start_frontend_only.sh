#!/bin/bash

# Frontend-only startup script for testing

set -e

echo "🎨 Starting StockX Frontend Only"
echo "================================="

# Check if Node.js is available
if ! command -v node &> /dev/null || ! command -v npm &> /dev/null; then
    echo "❌ Node.js/npm not found. Please install Node.js first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js detected"

# Navigate to frontend directory
cd frontend

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

echo "🌐 Starting React development server..."
echo ""
echo "🎨 Frontend Features:"
echo "   • Dark/Light/Auto theme switching"
echo "   • Responsive design"
echo "   • Interactive components"
echo "   • Material-UI components"
echo ""
echo "🌐 Application will open at: http://localhost:3000"
echo "📝 Note: Backend API calls will fail without the backend running"
echo ""

# Start React development server
npm start
