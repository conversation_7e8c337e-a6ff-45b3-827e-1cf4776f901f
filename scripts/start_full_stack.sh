#!/bin/bash

# Full-stack startup script for StockX (Backend + Frontend)

set -e

echo "🚀 Starting StockX Full-Stack Application"
echo "=========================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example"
    cp .env.example .env
    echo "📝 Please edit .env file with your Kite API credentials"
    echo "   Required: KITE_API_KEY, KITE_API_SECRET, KITE_ACCESS_TOKEN"
    exit 1
fi

# Check if Docker is available
if command -v docker-compose &> /dev/null; then
    echo "🐳 Docker Compose detected. Starting with Docker..."
    
    # Start all services with frontend
    echo "📦 Starting all services (Backend + Frontend + Redis + Worker)..."
    docker-compose --profile frontend up -d
    
    echo "✅ Services started successfully!"
    echo ""
    echo "🌐 Application URLs:"
    echo "   Frontend (React):     http://localhost:3000"
    echo "   Backend API:          http://localhost:8000"
    echo "   API Documentation:    http://localhost:8000/docs"
    echo "   Redis:                localhost:6379"
    echo ""
    echo "📊 Optional Monitoring:"
    echo "   Flower (Celery):      docker-compose --profile monitoring up -d"
    echo "   Then visit:           http://localhost:5555"
    echo ""
    echo "🔧 Useful Commands:"
    echo "   View logs:            docker-compose logs -f"
    echo "   Stop services:        docker-compose down"
    echo "   Restart:              docker-compose restart"
    
else
    echo "🔧 Docker not available. Starting manually..."
    
    # Check if Redis is running
    if ! redis-cli ping > /dev/null 2>&1; then
        echo "❌ Redis is not running. Please start Redis server first."
        echo "   macOS: brew services start redis"
        echo "   Ubuntu: sudo systemctl start redis"
        echo "   Manual: redis-server"
        exit 1
    fi
    
    echo "✅ Redis is running"
    
    # Start backend
    echo "🔄 Starting backend services..."
    
    # Install Python dependencies if needed
    if [ ! -d "venv" ]; then
        echo "📦 Creating Python virtual environment..."
        python -m venv venv
    fi
    
    echo "📦 Activating virtual environment..."
    source venv/bin/activate
    
    echo "📦 Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Start Celery worker in background
    echo "🔄 Starting Celery worker..."
    celery -A celery_app worker --loglevel=info --detach
    
    # Start FastAPI application in background
    echo "🌐 Starting FastAPI application..."
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    
    # Wait for backend to start
    echo "⏳ Waiting for backend to start..."
    sleep 5
    
    # Check if Node.js is available
    if command -v node &> /dev/null && command -v npm &> /dev/null; then
        echo "📦 Starting frontend..."
        
        # Navigate to frontend directory
        cd frontend
        
        # Install dependencies if needed
        if [ ! -d "node_modules" ]; then
            echo "📦 Installing Node.js dependencies..."
            npm install
        fi
        
        # Start React development server
        echo "🌐 Starting React development server..."
        npm start &
        FRONTEND_PID=$!
        
        cd ..
        
        echo "✅ Full-stack application started successfully!"
        echo ""
        echo "🌐 Application URLs:"
        echo "   Frontend (React):     http://localhost:3000"
        echo "   Backend API:          http://localhost:8000"
        echo "   API Documentation:    http://localhost:8000/docs"
        echo ""
        echo "🔧 To stop the application:"
        echo "   Press Ctrl+C or run: kill $BACKEND_PID $FRONTEND_PID"
        
        # Wait for user to stop
        echo ""
        echo "Press Ctrl+C to stop all services..."
        trap "echo 'Stopping services...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT
        wait
        
    else
        echo "❌ Node.js/npm not found. Please install Node.js to run the frontend."
        echo "   Visit: https://nodejs.org/"
        echo ""
        echo "✅ Backend is running at:"
        echo "   API:                  http://localhost:8000"
        echo "   Documentation:        http://localhost:8000/docs"
        echo ""
        echo "🔧 To stop the backend:"
        echo "   Press Ctrl+C or run: kill $BACKEND_PID"
        
        # Wait for user to stop
        trap "echo 'Stopping backend...'; kill $BACKEND_PID 2>/dev/null; exit" INT
        wait
    fi
fi
