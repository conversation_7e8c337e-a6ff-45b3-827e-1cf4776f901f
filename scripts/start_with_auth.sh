#!/bin/bash

# Full-stack startup script for StockX with Authentication

set -e

echo "🚀 Starting StockX with Authentication System"
echo "============================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example"
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration:"
    echo "   • Kite API credentials"
    echo "   • Database URL"
    echo "   • Secret key for JWT"
    echo "   • Email configuration"
    exit 1
fi

echo "✅ Environment configuration found"

# Check if Docker is available
if command -v docker-compose &> /dev/null; then
    echo "🐳 Docker Compose detected. Starting with Docker..."
    
    # Check if docker-compose.yml has database service
    if grep -q "postgres" docker-compose.yml 2>/dev/null; then
        echo "📦 Starting all services (Backend + Frontend + Database + Redis + Worker)..."
        docker-compose --profile frontend --profile database up -d
    else
        echo "📦 Starting services (Backend + Frontend + Redis + Worker)..."
        echo "⚠️  Note: Database service not found in docker-compose.yml"
        echo "   Please ensure PostgreSQL is running separately"
        docker-compose --profile frontend up -d
    fi
    
    echo "✅ Services started successfully!"
    echo ""
    echo "🌐 Application URLs:"
    echo "   Frontend (React):     http://localhost:3000"
    echo "   Backend API:          http://localhost:8000"
    echo "   API Documentation:    http://localhost:8000/docs"
    echo "   Authentication:       http://localhost:8000/api/v1/auth/"
    echo ""
    echo "🔐 Authentication Endpoints:"
    echo "   Register:             POST /api/v1/auth/register"
    echo "   Login:                POST /api/v1/auth/login"
    echo "   Profile:              GET  /api/v1/auth/me"
    echo "   Forgot Password:      POST /api/v1/auth/forgot-password"
    echo "   Reset Password:       POST /api/v1/auth/reset-password"
    echo ""
    echo "📊 Optional Monitoring:"
    echo "   Flower (Celery):      docker-compose --profile monitoring up -d"
    echo "   Then visit:           http://localhost:5555"
    
else
    echo "🔧 Docker not available. Starting manually..."
    
    # Check if PostgreSQL is running
    if ! pg_isready -h localhost -p 5432 &> /dev/null; then
        echo "❌ PostgreSQL is not running. Please start PostgreSQL first."
        echo "   macOS: brew services start postgresql"
        echo "   Ubuntu: sudo systemctl start postgresql"
        echo "   Manual: pg_ctl start"
        echo ""
        echo "🔧 Or run the database setup script:"
        echo "   ./scripts/setup_database.sh"
        exit 1
    fi
    
    echo "✅ PostgreSQL is running"
    
    # Check if Redis is running
    if ! redis-cli ping > /dev/null 2>&1; then
        echo "❌ Redis is not running. Please start Redis server first."
        echo "   macOS: brew services start redis"
        echo "   Ubuntu: sudo systemctl start redis"
        echo "   Manual: redis-server"
        exit 1
    fi
    
    echo "✅ Redis is running"
    
    # Start backend
    echo "🔄 Starting backend services..."
    
    # Install Python dependencies if needed
    if [ ! -d "venv" ]; then
        echo "📦 Creating Python virtual environment..."
        python -m venv venv
    fi
    
    echo "📦 Activating virtual environment..."
    source venv/bin/activate
    
    echo "📦 Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Run database migrations
    echo "🔄 Running database migrations..."
    if [ -f "alembic.ini" ]; then
        # Check if migrations directory exists
        if [ ! -d "migrations/versions" ]; then
            echo "📝 Creating initial migration..."
            alembic revision --autogenerate -m "Initial migration with auth tables"
        fi
        
        # Run migrations
        alembic upgrade head
        echo "✅ Database migrations completed"
    else
        echo "⚠️  Alembic not configured. Database tables may not exist."
    fi
    
    # Start Celery worker in background
    echo "🔄 Starting Celery worker..."
    celery -A celery_app worker --loglevel=info --detach
    
    # Start FastAPI application in background
    echo "🌐 Starting FastAPI application..."
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    
    # Wait for backend to start
    echo "⏳ Waiting for backend to start..."
    sleep 5
    
    # Test backend health
    if curl -s http://localhost:8000/health > /dev/null; then
        echo "✅ Backend is healthy"
    else
        echo "⚠️  Backend health check failed"
    fi
    
    # Check if Node.js is available
    if command -v node &> /dev/null && command -v npm &> /dev/null; then
        echo "📦 Starting frontend..."
        
        # Navigate to frontend directory
        cd frontend
        
        # Install dependencies if needed
        if [ ! -d "node_modules" ]; then
            echo "📦 Installing Node.js dependencies..."
            npm install
        fi
        
        # Start React development server
        echo "🌐 Starting React development server..."
        npm start &
        FRONTEND_PID=$!
        
        cd ..
        
        echo "✅ Full-stack application started successfully!"
        echo ""
        echo "🌐 Application URLs:"
        echo "   Frontend (React):     http://localhost:3000"
        echo "   Backend API:          http://localhost:8000"
        echo "   API Documentation:    http://localhost:8000/docs"
        echo ""
        echo "🔐 Authentication Features:"
        echo "   • User Registration & Login"
        echo "   • JWT Token Authentication"
        echo "   • Password Reset via Email"
        echo "   • User Profile Management"
        echo "   • Protected Routes"
        echo ""
        echo "🧪 Test Authentication:"
        echo "   1. Visit http://localhost:3000"
        echo "   2. Click 'Sign Up' to create an account"
        echo "   3. Login with your credentials"
        echo "   4. Access protected stock analysis features"
        echo ""
        echo "🔧 To stop the application:"
        echo "   Press Ctrl+C or run: kill $BACKEND_PID $FRONTEND_PID"
        
        # Wait for user to stop
        echo ""
        echo "Press Ctrl+C to stop all services..."
        trap "echo 'Stopping services...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT
        wait
        
    else
        echo "❌ Node.js/npm not found. Please install Node.js to run the frontend."
        echo "   Visit: https://nodejs.org/"
        echo ""
        echo "✅ Backend is running at:"
        echo "   API:                  http://localhost:8000"
        echo "   Documentation:        http://localhost:8000/docs"
        echo "   Authentication:       http://localhost:8000/api/v1/auth/"
        echo ""
        echo "🔧 To stop the backend:"
        echo "   Press Ctrl+C or run: kill $BACKEND_PID"
        
        # Wait for user to stop
        trap "echo 'Stopping backend...'; kill $BACKEND_PID 2>/dev/null; exit" INT
        wait
    fi
fi

echo ""
echo "🎉 StockX Authentication System Setup Complete!"
echo ""
echo "📚 Documentation:"
echo "   • API Docs: http://localhost:8000/docs"
echo "   • Authentication Guide: See README.md"
echo "   • Frontend Guide: See frontend/README.md"
