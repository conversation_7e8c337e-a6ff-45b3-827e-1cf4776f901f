#!/usr/bin/env python3
"""
Test script for StockX API endpoints.
"""

import requests
import time
import json
from typing import Dict, Any


class StockXAPITester:
    """Test client for StockX API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health(self) -> Dict[str, Any]:
        """Test basic health endpoint."""
        print("🏥 Testing health endpoint...")
        response = self.session.get(f"{self.base_url}/health")
        result = response.json()
        print(f"   Status: {response.status_code}")
        print(f"   Response: {json.dumps(result, indent=2)}")
        return result
    
    def test_exchanges(self) -> Dict[str, Any]:
        """Test exchanges endpoint."""
        print("🏢 Testing exchanges endpoint...")
        response = self.session.get(f"{self.base_url}/api/v1/exchanges")
        result = response.json()
        print(f"   Status: {response.status_code}")
        print(f"   Exchanges: {result['exchanges']}")
        return result
    
    def test_stock_data_request(self, symbol: str = "RELIANCE", exchange: str = "NSE") -> str:
        """Test stock data request."""
        print(f"📈 Testing stock data request for {symbol}...")
        
        params = {"symbol": symbol, "exchange": exchange}
        response = self.session.get(f"{self.base_url}/api/v1/get_stock_data", params=params)
        
        if response.status_code != 200:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
            return None
        
        result = response.json()
        task_id = result["task_id"]
        print(f"   ✅ Task submitted: {task_id}")
        print(f"   Status: {result['status']}")
        print(f"   Message: {result['message']}")
        
        return task_id
    
    def test_ltp_request(self, symbol: str = "RELIANCE", exchange: str = "NSE") -> str:
        """Test LTP request."""
        print(f"💰 Testing LTP request for {symbol}...")
        
        params = {"symbol": symbol, "exchange": exchange}
        response = self.session.get(f"{self.base_url}/api/v1/get_stock_ltp", params=params)
        
        if response.status_code != 200:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
            return None
        
        result = response.json()
        task_id = result["task_id"]
        print(f"   ✅ Task submitted: {task_id}")
        print(f"   Status: {result['status']}")
        print(f"   Message: {result['message']}")
        
        return task_id
    
    def test_task_status(self, task_id: str, max_wait: int = 30) -> Dict[str, Any]:
        """Test task status polling."""
        print(f"⏳ Polling task status for {task_id}...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            response = self.session.get(f"{self.base_url}/api/v1/task_status/{task_id}")
            
            if response.status_code != 200:
                print(f"   ❌ Error: {response.status_code} - {response.text}")
                return None
            
            result = response.json()
            status = result["status"]
            
            print(f"   Status: {status}")
            
            if status == "SUCCESS":
                print("   ✅ Task completed successfully!")
                if result.get("result"):
                    print(f"   Result: {json.dumps(result['result'], indent=2)}")
                return result
            
            elif status == "FAILURE":
                print("   ❌ Task failed!")
                if result.get("error"):
                    print(f"   Error: {result['error']}")
                return result
            
            elif status in ["PENDING", "STARTED"]:
                if result.get("progress"):
                    print(f"   Progress: {result['progress']}")
                time.sleep(2)
                continue
            
            else:
                print(f"   Unknown status: {status}")
                return result
        
        print(f"   ⏰ Timeout after {max_wait} seconds")
        return None
    
    def test_health_check_task(self) -> str:
        """Test health check task."""
        print("🔍 Testing health check task...")
        
        response = self.session.get(f"{self.base_url}/api/v1/health")
        
        if response.status_code != 200:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
            return None
        
        result = response.json()
        task_id = result["task_id"]
        print(f"   ✅ Health check task submitted: {task_id}")
        
        return task_id
    
    def run_full_test(self):
        """Run complete test suite."""
        print("🧪 Starting StockX API Test Suite")
        print("=" * 50)
        
        try:
            # Test basic endpoints
            self.test_health()
            print()
            
            self.test_exchanges()
            print()
            
            # Test health check task
            health_task_id = self.test_health_check_task()
            if health_task_id:
                print()
                self.test_task_status(health_task_id)
                print()
            
            # Test stock data request
            stock_task_id = self.test_stock_data_request()
            if stock_task_id:
                print()
                self.test_task_status(stock_task_id)
                print()
            
            # Test LTP request
            ltp_task_id = self.test_ltp_request()
            if ltp_task_id:
                print()
                self.test_task_status(ltp_task_id)
                print()
            
            print("✅ Test suite completed!")
            
        except Exception as e:
            print(f"❌ Test suite failed: {str(e)}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test StockX API")
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--symbol", default="RELIANCE", help="Stock symbol to test")
    parser.add_argument("--exchange", default="NSE", help="Exchange to test")
    
    args = parser.parse_args()
    
    tester = StockXAPITester(args.url)
    tester.run_full_test()
