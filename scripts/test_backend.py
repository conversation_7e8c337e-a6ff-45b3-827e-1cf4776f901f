#!/usr/bin/env python3
"""Test script to check backend startup and basic functionality."""

import sys
import os
import asyncio
import traceback

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_imports():
    """Test if all imports work correctly."""
    print("🔍 Testing imports...")
    
    try:
        # Test basic imports
        from app.config import settings
        print("✅ Config imported successfully")
        
        # Test database imports
        from app.database import Base, engine, database
        print("✅ Database imports successful")
        
        # Test model imports
        from app.models.auth import User, UserProfile, PasswordResetToken
        print("✅ Auth models imported successfully")
        
        # Test service imports
        from app.services.auth_service import AuthService
        from app.services.email_service import EmailService
        print("✅ Services imported successfully")
        
        # Test middleware imports
        from app.middleware.auth import get_current_user
        print("✅ Middleware imported successfully")
        
        # Test API imports
        from app.api.auth_routes import router as auth_router
        from app.api.stock_routes import router as stock_router
        print("✅ API routes imported successfully")
        
        # Test main app import
        from app.main import app
        print("✅ Main app imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {str(e)}")
        traceback.print_exc()
        return False

async def test_database_connection():
    """Test database connection."""
    print("\n🗄️  Testing database connection...")
    
    try:
        from app.database import database
        from app.config import settings
        
        print(f"Database URL: {settings.database_url}")
        
        # Try to connect
        await database.connect()
        print("✅ Database connection successful")
        
        # Try a simple query
        result = await database.fetch_one("SELECT 1 as test")
        if result and result['test'] == 1:
            print("✅ Database query successful")
        
        await database.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Database connection error: {str(e)}")
        return False

async def test_app_startup():
    """Test FastAPI app startup."""
    print("\n🚀 Testing FastAPI app startup...")
    
    try:
        from app.main import app
        from fastapi.testclient import TestClient
        
        # Create test client
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        if response.status_code == 200:
            print("✅ Root endpoint accessible")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
        
        # Test health endpoint
        response = client.get("/health")
        if response.status_code == 200:
            print("✅ Health endpoint accessible")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ App startup error: {str(e)}")
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🧪 StockX Backend Test Suite")
    print("=" * 40)
    
    # Test imports
    imports_ok = await test_imports()
    
    if not imports_ok:
        print("\n❌ Import tests failed. Cannot continue.")
        return False
    
    # Test database (optional - may fail if DB not running)
    db_ok = await test_database_connection()
    if not db_ok:
        print("⚠️  Database tests failed (this is OK if DB is not running)")
    
    # Test app startup
    app_ok = await test_app_startup()
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    print(f"   Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"   Database: {'✅ PASS' if db_ok else '⚠️  SKIP'}")
    print(f"   App Startup: {'✅ PASS' if app_ok else '❌ FAIL'}")
    
    if imports_ok and app_ok:
        print("\n🎉 Backend is ready to start!")
        return True
    else:
        print("\n❌ Backend has issues that need to be fixed.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
