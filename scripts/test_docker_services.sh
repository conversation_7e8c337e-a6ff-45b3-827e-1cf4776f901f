#!/bin/bash

# Test Docker services for StockX

set -e

echo "🧪 Testing StockX Docker Services"
echo "================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running"
    exit 1
fi

echo "✅ Docker is running"

# Check if services are running
echo ""
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🔍 Testing Service Health:"

# Test PostgreSQL
echo "Testing PostgreSQL..."
if docker-compose exec -T postgres pg_isready -U stockx -d stockx > /dev/null 2>&1; then
    echo "✅ PostgreSQL is healthy"
else
    echo "❌ PostgreSQL is not responding"
fi

# Test Redis
echo "Testing Redis..."
if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis is healthy"
else
    echo "❌ Redis is not responding"
fi

# Test API
echo "Testing API..."
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ API is responding"
    echo "API Response:"
    curl -s http://localhost:8000/health | jq . 2>/dev/null || curl -s http://localhost:8000/health
else
    echo "❌ API is not responding"
    echo "API Logs:"
    docker-compose logs --tail=10 api
fi

# Test Frontend
echo "Testing Frontend..."
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is responding"
else
    echo "❌ Frontend is not responding"
    echo "Frontend Logs:"
    docker-compose logs --tail=10 frontend
fi

echo ""
echo "🔗 Service URLs:"
echo "   Frontend:     http://localhost:3000"
echo "   Backend API:  http://localhost:8000"
echo "   API Docs:     http://localhost:8000/docs"
echo "   Health:       http://localhost:8000/health"

echo ""
echo "📋 Recent Logs:"
echo "API Logs:"
docker-compose logs --tail=5 api
echo ""
echo "Worker Logs:"
docker-compose logs --tail=5 worker
