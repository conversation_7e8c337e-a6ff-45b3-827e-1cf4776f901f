"""Tests for StockX API endpoints."""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.main import app
from app.models.stock import Exchange


client = TestClient(app)


class TestBasicEndpoints:
    """Test basic API endpoints."""
    
    def test_root_endpoint(self):
        """Test root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
    
    def test_health_endpoint(self):
        """Test health endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
    
    def test_exchanges_endpoint(self):
        """Test exchanges endpoint."""
        response = client.get("/api/v1/exchanges")
        assert response.status_code == 200
        data = response.json()
        assert "exchanges" in data
        assert "default" in data
        assert Exchange.NSE.value in data["exchanges"]


class TestStockEndpoints:
    """Test stock-related endpoints."""
    
    @patch('app.tasks.stock_tasks.fetch_stock_data.delay')
    def test_get_stock_data_success(self, mock_task):
        """Test successful stock data request."""
        # Mock Celery task
        mock_task.return_value.id = "test-task-id"
        
        response = client.get("/api/v1/get_stock_data?symbol=RELIANCE&exchange=NSE")
        assert response.status_code == 200
        
        data = response.json()
        assert data["task_id"] == "test-task-id"
        assert data["status"] == "PENDING"
        assert "RELIANCE" in data["message"]
        
        # Verify task was called with correct parameters
        mock_task.assert_called_once_with("RELIANCE", "NSE")
    
    @patch('app.tasks.stock_tasks.fetch_stock_ltp.delay')
    def test_get_stock_ltp_success(self, mock_task):
        """Test successful LTP request."""
        # Mock Celery task
        mock_task.return_value.id = "test-ltp-task-id"
        
        response = client.get("/api/v1/get_stock_ltp?symbol=INFY&exchange=NSE")
        assert response.status_code == 200
        
        data = response.json()
        assert data["task_id"] == "test-ltp-task-id"
        assert data["status"] == "PENDING"
        assert "INFY" in data["message"]
        
        # Verify task was called with correct parameters
        mock_task.assert_called_once_with("INFY", "NSE")
    
    def test_get_stock_data_invalid_symbol(self):
        """Test stock data request with invalid symbol."""
        response = client.get("/api/v1/get_stock_data?symbol=&exchange=NSE")
        assert response.status_code == 400
        assert "Symbol cannot be empty" in response.json()["detail"]
    
    @patch('celery_app.celery_app.AsyncResult')
    def test_task_status_pending(self, mock_result):
        """Test task status for pending task."""
        # Mock Celery AsyncResult
        mock_result.return_value.state = 'PENDING'
        
        response = client.get("/api/v1/task_status/test-task-id")
        assert response.status_code == 200
        
        data = response.json()
        assert data["task_id"] == "test-task-id"
        assert data["status"] == "PENDING"
        assert data["result"] is None
    
    @patch('celery_app.celery_app.AsyncResult')
    def test_task_status_success(self, mock_result):
        """Test task status for successful task."""
        # Mock successful task result
        mock_result.return_value.state = 'SUCCESS'
        mock_result.return_value.result = {
            'status': 'SUCCESS',
            'data': {
                'last_price': 2450.50,
                'volume': 1234567,
                'symbol': 'RELIANCE'
            }
        }
        
        response = client.get("/api/v1/task_status/test-task-id")
        assert response.status_code == 200
        
        data = response.json()
        assert data["task_id"] == "test-task-id"
        assert data["status"] == "SUCCESS"
        assert data["result"]["last_price"] == 2450.50
        assert data["error"] is None
    
    @patch('celery_app.celery_app.AsyncResult')
    def test_task_status_failure(self, mock_result):
        """Test task status for failed task."""
        # Mock failed task result
        mock_result.return_value.state = 'FAILURE'
        mock_result.return_value.info = {'error': 'API connection failed'}
        
        response = client.get("/api/v1/task_status/test-task-id")
        assert response.status_code == 200
        
        data = response.json()
        assert data["task_id"] == "test-task-id"
        assert data["status"] == "FAILURE"
        assert data["result"] is None
        assert "API connection failed" in data["error"]
    
    @patch('app.tasks.stock_tasks.health_check.delay')
    def test_health_check_endpoint(self, mock_task):
        """Test health check endpoint."""
        # Mock Celery task
        mock_task.return_value.id = "health-task-id"
        
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["task_id"] == "health-task-id"
        assert data["status"] == "PENDING"
        assert "Health check" in data["message"]


class TestValidation:
    """Test input validation."""
    
    def test_invalid_exchange(self):
        """Test with invalid exchange."""
        response = client.get("/api/v1/get_stock_data?symbol=RELIANCE&exchange=INVALID")
        assert response.status_code == 422  # Validation error
    
    def test_missing_symbol(self):
        """Test with missing symbol parameter."""
        response = client.get("/api/v1/get_stock_data?exchange=NSE")
        assert response.status_code == 422  # Validation error
    
    def test_default_exchange(self):
        """Test default exchange value."""
        with patch('app.tasks.stock_tasks.fetch_stock_data.delay') as mock_task:
            mock_task.return_value.id = "test-task-id"
            
            response = client.get("/api/v1/get_stock_data?symbol=RELIANCE")
            assert response.status_code == 200
            
            # Should use default NSE exchange
            mock_task.assert_called_once_with("RELIANCE", "NSE")
